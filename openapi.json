{"openapi": "3.0.3", "info": {"title": "SERP Payload API", "version": "1.0.0", "description": "Auto-generated API documentation for SERP Payload CMS"}, "paths": {"/api/media": {"get": {"summary": "Retrieve a list of Media", "tags": ["Media"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "number"}}, {"in": "query", "name": "limit", "schema": {"type": "number"}}, {"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "query", "name": "sort", "schema": {"type": "string", "enum": ["alt", "-alt", "prefix", "-prefix", "updatedAt", "-updatedAt", "createdAt", "-createdAt", "url", "-url", "thumbnailURL", "-thumbnailURL", "filename", "-filename", "mimeType", "-mimeType", "filesize", "-filesize", "width", "-width", "height", "-height", "focalX", "-focalX", "focalY", "-focalY"]}}, {"in": "query", "name": "where", "style": "deepObject", "schema": {"allOf": [{"type": "object"}, {"anyOf": [{"$ref": "#/components/schemas/MediaQueryOperations"}, {"$ref": "#/components/schemas/MediaQueryOperationsAnd"}, {"$ref": "#/components/schemas/MediaQueryOperationsOr"}]}]}}], "responses": {"200": {"$ref": "#/components/responses/MediaListResponse"}}, "security": []}, "post": {"summary": "Create a new Media", "tags": ["Media"], "requestBody": {"$ref": "#/components/requestBodies/MediaRequestBody"}, "responses": {"201": {"$ref": "#/components/responses/NewMediaResponse"}}, "security": [{"ApiKey": []}]}}, "/api/media/{id}": {"parameters": [{"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "path", "name": "id", "description": "ID of the Media", "required": true, "schema": {"type": "string"}}], "get": {"summary": "Find a Media by ID", "tags": ["Media"], "responses": {"200": {"$ref": "#/components/responses/MediaResponse"}, "404": {"$ref": "#/components/responses/MediaNotFoundResponse"}}, "security": []}, "patch": {"summary": "Update a Media", "tags": ["Media"], "responses": {"200": {"$ref": "#/components/responses/MediaResponse"}, "404": {"$ref": "#/components/responses/MediaNotFoundResponse"}}, "security": [{"ApiKey": []}]}, "delete": {"summary": "Delete a Media", "tags": ["Media"], "responses": {"200": {"$ref": "#/components/responses/MediaResponse"}, "404": {"$ref": "#/components/responses/MediaNotFoundResponse"}}, "security": [{"ApiKey": []}]}}, "/api/users": {"get": {"summary": "Retrieve a list of Users", "tags": ["Users"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "number"}}, {"in": "query", "name": "limit", "schema": {"type": "number"}}, {"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "query", "name": "sort", "schema": {"type": "string", "enum": ["id", "-id", "email", "-email", "firstName", "-first<PERSON><PERSON>", "lastName", "-last<PERSON><PERSON>", "_otp", "-_otp", "_otpExpiration", "-_otpExpiration", "updatedAt", "-updatedAt", "createdAt", "-createdAt"]}}, {"in": "query", "name": "where", "style": "deepObject", "schema": {"allOf": [{"type": "object"}, {"anyOf": [{"$ref": "#/components/schemas/UserQueryOperations"}, {"$ref": "#/components/schemas/UserQueryOperationsAnd"}, {"$ref": "#/components/schemas/UserQueryOperationsOr"}]}]}}], "responses": {"200": {"$ref": "#/components/responses/UserListResponse"}}, "security": [{"ApiKey": []}]}, "post": {"summary": "Create a new User", "tags": ["Users"], "requestBody": {"$ref": "#/components/requestBodies/UserRequestBody"}, "responses": {"201": {"$ref": "#/components/responses/NewUserResponse"}}, "security": []}}, "/api/users/{id}": {"parameters": [{"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "path", "name": "id", "description": "ID of the User", "required": true, "schema": {"type": "string"}}], "get": {"summary": "Find a User by ID", "tags": ["Users"], "responses": {"200": {"$ref": "#/components/responses/UserResponse"}, "404": {"$ref": "#/components/responses/UserNotFoundResponse"}}, "security": [{"ApiKey": []}]}, "patch": {"summary": "Update a User", "tags": ["Users"], "responses": {"200": {"$ref": "#/components/responses/UserResponse"}, "404": {"$ref": "#/components/responses/UserNotFoundResponse"}}, "security": [{"ApiKey": []}]}, "delete": {"summary": "Delete a User", "tags": ["Users"], "responses": {"200": {"$ref": "#/components/responses/UserResponse"}, "404": {"$ref": "#/components/responses/UserNotFoundResponse"}}, "security": [{"ApiKey": []}]}}, "/api/teams": {"get": {"summary": "Retrieve a list of Teams", "tags": ["Teams"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "number"}}, {"in": "query", "name": "limit", "schema": {"type": "number"}}, {"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "query", "name": "sort", "schema": {"type": "string", "enum": ["name", "-name", "updatedAt", "-updatedAt", "createdAt", "-createdAt"]}}, {"in": "query", "name": "where", "style": "deepObject", "schema": {"allOf": [{"type": "object"}, {"anyOf": [{"$ref": "#/components/schemas/TeamQueryOperations"}, {"$ref": "#/components/schemas/TeamQueryOperationsAnd"}, {"$ref": "#/components/schemas/TeamQueryOperationsOr"}]}]}}], "responses": {"200": {"$ref": "#/components/responses/TeamListResponse"}}, "security": [{"ApiKey": []}]}, "post": {"summary": "Create a new Team", "tags": ["Teams"], "requestBody": {"$ref": "#/components/requestBodies/TeamRequestBody"}, "responses": {"201": {"$ref": "#/components/responses/NewTeamResponse"}}, "security": [{"ApiKey": []}]}}, "/api/teams/{id}": {"parameters": [{"in": "query", "name": "depth", "schema": {"type": "number"}}, {"in": "query", "name": "locale", "schema": {"type": "string"}}, {"in": "query", "name": "fallback-locale", "schema": {"type": "string"}}, {"in": "path", "name": "id", "description": "ID of the Team", "required": true, "schema": {"type": "string"}}], "get": {"summary": "Find a Team by ID", "tags": ["Teams"], "responses": {"200": {"$ref": "#/components/responses/TeamResponse"}, "404": {"$ref": "#/components/responses/TeamNotFoundResponse"}}, "security": [{"ApiKey": []}]}, "patch": {"summary": "Update a Team", "tags": ["Teams"], "responses": {"200": {"$ref": "#/components/responses/TeamResponse"}, "404": {"$ref": "#/components/responses/TeamNotFoundResponse"}}, "security": [{"ApiKey": []}]}, "delete": {"summary": "Delete a Team", "tags": ["Teams"], "responses": {"200": {"$ref": "#/components/responses/TeamResponse"}, "404": {"$ref": "#/components/responses/TeamNotFoundResponse"}}, "security": [{"ApiKey": []}]}}, "/api/users/otp/request": {"post": {"summary": "Request OTP for login", "description": "Send OTP to user email for authentication", "tags": ["Users"], "requestBody": {"$ref": "#/components/requestBodies/UserOtpRequestRequestBody"}, "responses": {"201": {"$ref": "#/components/responses/UserOtpRequestResponse"}, "400": {"description": "Invalid email format or missing email"}, "500": {"description": "Failed to send OTP email"}}, "security": [{"ApiKey": []}]}}, "/api/users/otp/login": {"post": {"summary": "Login with OTP", "description": "Authenticate user using email and OTP", "tags": ["Users"], "requestBody": {"$ref": "#/components/requestBodies/UserOtpLoginRequestBody"}, "responses": {"201": {"$ref": "#/components/responses/UserOtpLoginResponse"}, "401": {"description": "Invalid OTP or expired"}, "404": {"description": "User not found"}}, "security": [{"ApiKey": []}]}}, "/api/users/refresh-token": {"post": {"summary": "Refresh authentication token", "description": "Get new access token using refresh token", "tags": ["Users"], "responses": {"201": {"$ref": "#/components/responses/UserRefreshTokenResponse"}, "401": {"description": "Invalid or expired refresh token"}}, "security": [{"ApiKey": []}]}}}, "components": {"securitySchemes": {"ApiKey": {"type": "oauth2", "flows": {"password": {"tokenUrl": "/api/users/login", "scopes": {}}}}}, "schemas": {"supportedTimezones": {"type": "string", "example": "Europe/Prague"}, "GenericObject": {"type": "object", "title": "Generic Object", "description": "Reference to a filtered collection or external resource", "properties": {"id": {"type": "string"}}, "additionalProperties": true}, "Media": {"type": "object", "additionalProperties": false, "title": "Media", "properties": {"id": {"type": "string"}, "alt": {"type": "string", "nullable": true}, "caption": {"type": "object", "properties": {"root": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string"}, "children": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"type": {"type": "string"}, "version": {"type": "integer"}}, "required": ["type", "version"]}}, "direction": {"oneOf": [{"enum": ["ltr", "rtl"], "nullable": true}]}, "format": {"type": "string", "enum": ["left", "start", "center", "right", "end", "justify", ""]}, "indent": {"type": "integer"}, "version": {"type": "integer"}}, "required": ["children", "direction", "format", "indent", "type", "version"]}}, "required": ["root"], "nullable": true}, "prefix": {"type": "string", "nullable": true}, "updatedAt": {"type": "string"}, "createdAt": {"type": "string"}, "url": {"type": "string", "nullable": true}, "thumbnailURL": {"type": "string", "nullable": true}, "filename": {"type": "string", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "focalX": {"type": "number", "nullable": true}, "focalY": {"type": "number", "nullable": true}, "sizes": {"type": "object", "additionalProperties": false, "properties": {"thumbnail": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}}, "required": []}, "square": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}}, "required": []}, "small": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}}, "required": []}, "medium": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}}, "required": []}, "large": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}}, "required": []}, "xlarge": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}}, "required": []}, "og": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}}, "required": []}}, "required": []}}, "required": ["id", "updatedAt", "createdAt"]}, "User": {"type": "object", "additionalProperties": false, "title": "User", "properties": {"id": {"type": "string"}, "email": {"type": "string"}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "profileImage": {"description": "Upload a profile image for the user", "oneOf": [{"type": "string", "nullable": true}, {"$ref": "#/components/schemas/Media"}]}, "socialAccounts": {"description": "Social accounts for the user", "type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"provider": {"type": "string"}, "providerId": {"type": "string"}, "isActive": {"type": "boolean", "nullable": true}, "linkedAt": {"type": "string", "nullable": true}, "id": {"type": "string", "nullable": true}}, "required": ["provider", "providerId"]}, "nullable": true}, "teams": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"team": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Team"}]}, "roles": {"type": "array", "items": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "teamviewer"]}}, "id": {"type": "string", "nullable": true}}, "required": ["team", "roles"]}, "nullable": true}, "_otp": {"type": "string", "nullable": true}, "_otpExpiration": {"type": "string", "nullable": true}, "updatedAt": {"type": "string"}, "createdAt": {"type": "string"}}, "required": ["id", "email", "updatedAt", "createdAt"]}, "Team": {"type": "object", "additionalProperties": false, "title": "Team", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "image": {"oneOf": [{"type": "string", "nullable": true}, {"$ref": "#/components/schemas/Media"}]}, "user": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/User"}]}, "updatedAt": {"type": "string"}, "createdAt": {"type": "string"}}, "required": ["id", "name", "user", "updatedAt", "createdAt"]}, "MediaQueryOperations": {"title": "Media query operations", "type": "object", "properties": {"alt": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "prefix": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "updatedAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}, "createdAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}, "url": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "thumbnailURL": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "filename": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "mimeType": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "filesize": {"type": "object", "properties": {"equals": {"type": "number"}, "not_equals": {"type": "number"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "number"}, "greater_than_equal": {"type": "number"}, "less_than": {"type": "number"}, "less_than_equal": {"type": "number"}}}, "width": {"type": "object", "properties": {"equals": {"type": "number"}, "not_equals": {"type": "number"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "number"}, "greater_than_equal": {"type": "number"}, "less_than": {"type": "number"}, "less_than_equal": {"type": "number"}}}, "height": {"type": "object", "properties": {"equals": {"type": "number"}, "not_equals": {"type": "number"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "number"}, "greater_than_equal": {"type": "number"}, "less_than": {"type": "number"}, "less_than_equal": {"type": "number"}}}, "focalX": {"type": "object", "properties": {"equals": {"type": "number"}, "not_equals": {"type": "number"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "number"}, "greater_than_equal": {"type": "number"}, "less_than": {"type": "number"}, "less_than_equal": {"type": "number"}}}, "focalY": {"type": "object", "properties": {"equals": {"type": "number"}, "not_equals": {"type": "number"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "number"}, "greater_than_equal": {"type": "number"}, "less_than": {"type": "number"}, "less_than_equal": {"type": "number"}}}}}, "MediaQueryOperationsAnd": {"title": "Media query conjunction", "type": "object", "properties": {"and": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/MediaQueryOperations"}, {"$ref": "#/components/schemas/MediaQueryOperationsAnd"}, {"$ref": "#/components/schemas/MediaQueryOperationsOr"}]}}}, "required": ["and"]}, "MediaQueryOperationsOr": {"title": "Media query disjunction", "type": "object", "properties": {"or": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/MediaQueryOperations"}, {"$ref": "#/components/schemas/MediaQueryOperationsAnd"}, {"$ref": "#/components/schemas/MediaQueryOperationsOr"}]}}}, "required": ["or"]}, "UserQueryOperations": {"title": "User query operations", "type": "object", "properties": {"id": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "email": {"type": "object", "properties": {"equals": {"type": "string", "format": "email"}, "not_equals": {"type": "string", "format": "email"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "contains": {"type": "string", "format": "email"}}}, "firstName": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "lastName": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "_otp": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "_otpExpiration": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}, "updatedAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}, "createdAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}}}, "UserQueryOperationsAnd": {"title": "User query conjunction", "type": "object", "properties": {"and": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/UserQueryOperations"}, {"$ref": "#/components/schemas/UserQueryOperationsAnd"}, {"$ref": "#/components/schemas/UserQueryOperationsOr"}]}}}, "required": ["and"]}, "UserQueryOperationsOr": {"title": "User query disjunction", "type": "object", "properties": {"or": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/UserQueryOperations"}, {"$ref": "#/components/schemas/UserQueryOperationsAnd"}, {"$ref": "#/components/schemas/UserQueryOperationsOr"}]}}}, "required": ["or"]}, "TeamQueryOperations": {"title": "Team query operations", "type": "object", "properties": {"name": {"type": "object", "properties": {"equals": {"type": "string"}, "not_equals": {"type": "string"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "like": {"type": "string"}, "contains": {"type": "string"}}}, "updatedAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}, "createdAt": {"type": "object", "properties": {"equals": {"type": "string", "format": "date-time"}, "not_equals": {"type": "string", "format": "date-time"}, "in": {"type": "string"}, "not_in": {"type": "string"}, "greater_than": {"type": "string", "format": "date-time"}, "greater_than_equal": {"type": "string", "format": "date-time"}, "less_than": {"type": "string", "format": "date-time"}, "less_than_equal": {"type": "string", "format": "date-time"}}}}}, "TeamQueryOperationsAnd": {"title": "Team query conjunction", "type": "object", "properties": {"and": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/TeamQueryOperations"}, {"$ref": "#/components/schemas/TeamQueryOperationsAnd"}, {"$ref": "#/components/schemas/TeamQueryOperationsOr"}]}}}, "required": ["and"]}, "TeamQueryOperationsOr": {"title": "Team query disjunction", "type": "object", "properties": {"or": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/TeamQueryOperations"}, {"$ref": "#/components/schemas/TeamQueryOperationsAnd"}, {"$ref": "#/components/schemas/TeamQueryOperationsOr"}]}}}, "required": ["or"]}, "UserOtpRequestRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}}, "required": ["email"], "title": "User OTP request"}, "UserOtpLoginRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "otp": {"type": "string", "minLength": 6, "maxLength": 6}}, "required": ["email", "otp"], "title": "User OTP login request"}, "UserOtpRequestResponse": {"type": "object", "properties": {"type": {"type": "string", "example": "Email"}, "message": {"type": "string", "example": "Successfully sent one-time password."}, "value": {"type": "string", "format": "email"}}, "required": ["type", "message", "value"], "title": "User OTP request response"}, "UserOtpLoginResponse": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/User"}, "token": {"type": "string"}, "exp": {"type": "number"}}, "required": ["user", "token", "exp"], "title": "User OTP login response"}, "UserRefreshTokenResponse": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/User"}, "token": {"type": "string"}, "exp": {"type": "number"}}, "required": ["user", "token", "exp"], "title": "User refresh token response"}}, "requestBodies": {"MediaRequestBody": {"description": "Media", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "title": "Media", "properties": {"alt": {"type": "string", "nullable": true}, "caption": {"type": "object", "properties": {"root": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string"}, "children": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"type": {"type": "string"}, "version": {"type": "integer"}}, "required": ["type", "version"]}}, "direction": {"oneOf": [{"enum": ["ltr", "rtl"], "nullable": true}]}, "format": {"type": "string", "enum": ["left", "start", "center", "right", "end", "justify", ""]}, "indent": {"type": "integer"}, "version": {"type": "integer"}}, "required": ["children", "direction", "format", "indent", "type", "version"]}}, "required": ["root"], "nullable": true}, "prefix": {"type": "string", "nullable": true}, "url": {"type": "string", "nullable": true}, "thumbnailURL": {"type": "string", "nullable": true}, "filename": {"type": "string", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "focalX": {"type": "number", "nullable": true}, "focalY": {"type": "number", "nullable": true}, "sizes": {"type": "object", "additionalProperties": false, "properties": {"thumbnail": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}}, "required": []}, "square": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}}, "required": []}, "small": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}}, "required": []}, "medium": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}}, "required": []}, "large": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}}, "required": []}, "xlarge": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}}, "required": []}, "og": {"type": "object", "additionalProperties": false, "properties": {"url": {"type": "string", "nullable": true}, "width": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "mimeType": {"type": "string", "nullable": true}, "filesize": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}}, "required": []}}, "required": []}}, "required": ["id", "updatedAt", "createdAt"]}}}}, "UserRequestBody": {"description": "User", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "title": "User", "properties": {"email": {"type": "string"}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "profileImage": {"description": "Upload a profile image for the user", "oneOf": [{"type": "string", "nullable": true}, {"$ref": "#/components/schemas/Media"}]}, "socialAccounts": {"description": "Social accounts for the user", "type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"provider": {"type": "string"}, "providerId": {"type": "string"}, "isActive": {"type": "boolean", "nullable": true}, "linkedAt": {"type": "string", "nullable": true}, "id": {"type": "string", "nullable": true}}, "required": ["provider", "providerId"]}, "nullable": true}, "teams": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"team": {"oneOf": [{"type": "string"}, {"$ref": "#/components/schemas/Team"}]}, "roles": {"type": "array", "items": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "teamviewer"]}}, "id": {"type": "string", "nullable": true}}, "required": ["team", "roles"]}, "nullable": true}, "_otp": {"type": "string", "nullable": true}, "_otpExpiration": {"type": "string", "nullable": true}}, "required": ["id", "email", "updatedAt", "createdAt"]}}}}, "TeamRequestBody": {"description": "Team", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "title": "Team", "properties": {"name": {"type": "string"}, "image": {"oneOf": [{"type": "string", "nullable": true}, {"$ref": "#/components/schemas/Media"}]}, "user": {"type": "string", "description": "ID of the users"}}, "required": ["id", "name", "user", "updatedAt", "createdAt"]}}}}, "UserOtpRequestRequestBody": {"description": "Send OTP to user email for authentication", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserOtpRequestRequest"}}}}, "UserOtpLoginRequestBody": {"description": "Authenticate user using email and OTP", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserOtpLoginRequest"}}}}}, "responses": {"MediaResponse": {"description": "Media object", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Media"}}}}, "NewMediaResponse": {"description": "Media object", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "doc": {"allOf": [{"$ref": "#/components/schemas/Media"}, {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "createdAt", "updatedAt"]}]}}, "required": ["message", "doc"]}}}}, "MediaNotFoundResponse": {"description": "Media not found", "content": {}}, "MediaListResponse": {"description": "List of Media", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array", "items": {"$ref": "#/components/schemas/Media"}}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}, "required": ["docs", "totalDocs", "limit", "totalPages", "page", "pagingCounter", "hasPrevPage", "hasNextPage", "prevPage", "nextPage"]}}}}, "UserResponse": {"description": "User object", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "NewUserResponse": {"description": "User object", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "doc": {"allOf": [{"$ref": "#/components/schemas/User"}, {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "createdAt", "updatedAt"]}]}}, "required": ["message", "doc"]}}}}, "UserNotFoundResponse": {"description": "User not found", "content": {}}, "UserListResponse": {"description": "List of Users", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}, "required": ["docs", "totalDocs", "limit", "totalPages", "page", "pagingCounter", "hasPrevPage", "hasNextPage", "prevPage", "nextPage"]}}}}, "TeamResponse": {"description": "Team object", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Team"}}}}, "NewTeamResponse": {"description": "Team object", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "doc": {"allOf": [{"$ref": "#/components/schemas/Team"}, {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "createdAt", "updatedAt"]}]}}, "required": ["message", "doc"]}}}}, "TeamNotFoundResponse": {"description": "Team not found", "content": {}}, "TeamListResponse": {"description": "List of Teams", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array", "items": {"$ref": "#/components/schemas/Team"}}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}, "required": ["docs", "totalDocs", "limit", "totalPages", "page", "pagingCounter", "hasPrevPage", "hasNextPage", "prevPage", "nextPage"]}}}}, "UserOtpRequestResponse": {"description": "User otpRequest response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserOtpRequestResponse"}}}}, "UserOtpLoginResponse": {"description": "User otpLogin response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserOtpLoginResponse"}}}}, "UserRefreshTokenResponse": {"description": "User refreshToken response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserRefreshTokenResponse"}}}}}}}