name: serplens
services:
  nginx:
    image: nginx:latest
    ports:
      - 80:80
      - 443:443
    restart: always
    volumes:
      - ./nginx/conf/:/etc/nginx/conf.d/:ro
      - ./nginx/certs:/etc/nginx/ssl
    attach: false
    logging:
      driver: 'none'

  postgres:
    restart: always
    image: postgres:17
    volumes:
      - pgdata:/var/lib/postgresql/data
    ports:
      - '54320:5432'
    environment:
      POSTGRES_USER: postgres
      POSTGRES_DB: serplens_db
      POSTGRES_HOST_AUTH_METHOD: trust

volumes:
  pgdata:
  # data:
  node_modules:
