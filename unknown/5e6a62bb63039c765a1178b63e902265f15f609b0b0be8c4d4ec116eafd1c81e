import type { Block } from 'payload'
import { collections } from '@/constants'
import { linkGroup } from '@/fields'
import {
  FixedToolbarFeature,
  HeadingFeature,
  InlineToolbarFeature,
  lexicalEditor
} from '@payloadcms/richtext-lexical'

const {
  blocks: {
    callToAction: { slug: callToActionSlug }
  }
} = collections

export const callToAction: Block = {
  slug: callToActionSlug,
  interfaceName: 'CallToActionBlock',
  fields: [
    {
      name: 'richText',
      type: 'richText',
      editor: lexicalEditor({
        features: ({ rootFeatures }) => {
          return [
            ...rootFeatures,
            HeadingFeature({ enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4'] }),
            FixedToolbarFeature(),
            InlineToolbarFeature()
          ]
        }
      }),
      label: false
    },
    linkGroup({
      appearances: ['default', 'outline'],
      overrides: {
        maxRows: 2
      }
    })
  ],
  labels: {
    plural: 'Calls to Action',
    singular: 'Call to Action'
  }
}
