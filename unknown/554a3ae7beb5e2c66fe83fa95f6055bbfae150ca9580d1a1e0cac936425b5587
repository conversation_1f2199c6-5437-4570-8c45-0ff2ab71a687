import type { Block } from 'payload'
import { collections } from '@/constants'

const {
  blocks: {
    code: { slug: codeSlug }
  }
} = collections

export const code: Block = {
  slug: codeSlug,
  interfaceName: 'CodeBlock',
  fields: [
    {
      name: 'language',
      type: 'select',
      defaultValue: 'typescript',
      options: [
        {
          label: 'Typescript',
          value: 'typescript'
        },
        {
          label: 'Javascript',
          value: 'javascript'
        },
        {
          label: 'CSS',
          value: 'css'
        }
      ]
    },
    {
      name: 'code',
      type: 'code',
      label: false,
      required: true
    }
  ]
}
