import path from 'path'
import { PayloadRequest, buildConfig } from 'payload'
import sharp from 'sharp'
import { fileURLToPath } from 'url'
import {
  admins,
  assets,
  categories,
  footer,
  header,
  keywords,
  media,
  pages,
  posts,
  products,
  projects,
  screenshots,
  subscriptions,
  teams,
  users
} from '@/collections'
import { collections } from '@/constants'
import { endpoints } from '@/endpoints'
import { defaultLexical } from '@/fields'
import { plugins } from '@/plugins'
import { getServerSideURL } from '@/utils/server'
import { vercelPostgresAdapter } from '@payloadcms/db-vercel-postgres'
import { resendAdapter } from '@payloadcms/email-resend'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

const {
  admins: { slug: adminsSlug }
} = collections

export default buildConfig({
  admin: {
    importMap: {
      baseDir: path.resolve(dirname)
    },
    user: adminsSlug,
    livePreview: {
      breakpoints: [
        {
          label: 'Mobile',
          name: 'mobile',
          width: 375,
          height: 667
        },
        {
          label: 'Tablet',
          name: 'tablet',
          width: 768,
          height: 1024
        },
        {
          label: 'Desktop',
          name: 'desktop',
          width: 1440,
          height: 900
        }
      ]
    }
  },

  editor: defaultLexical,
  db: vercelPostgresAdapter({
    pool: {
      connectionString: process.env.POSTGRES_URL || ''
    }
  }),
  collections: [
    admins,
    pages,
    posts,
    media,
    categories,
    users,
    teams,
    projects,
    keywords,
    assets,
    screenshots,
    products,
    subscriptions
  ],
  cors: [getServerSideURL()].filter(Boolean),
  endpoints,
  globals: [header, footer],
  plugins: [...plugins],
  secret: process.env.PAYLOAD_SECRET,
  sharp,
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts')
  },
  jobs: {
    access: {
      run: ({ req }: { req: PayloadRequest }): boolean => {
        // Allow logged in users to execute this endpoint (default)
        if (req.user) return true

        // If there is no logged in user, then check
        // for the Vercel Cron secret to be present as an
        // Authorization header:
        const authHeader = req.headers.get('authorization')
        return authHeader === `Bearer ${process.env.CRON_SECRET}`
      }
    },
    tasks: []
  },
  email: resendAdapter({
    defaultFromAddress: process.env.FROM_ADDRESS || '',
    defaultFromName: process.env.FROM_NAME || '',
    apiKey: process.env.RESEND_API_KEY || ''
  }),
  serverURL: getServerSideURL()
})
