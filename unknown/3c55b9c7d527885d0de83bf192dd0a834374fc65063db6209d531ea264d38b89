import type { Team, User } from '@/payload-types'
import { extractId } from './extractId'

/**
 * Returns array of all team IDs assigned to a user
 *
 * @param user - User object with teams field
 * @param role - Optional role to filter by
 */
export const getUserTeamIds = (
  user: null | User,
  role?: NonNullable<User['teams']>[number]['roles'][number]
): Team['id'][] => {
  if (!user) {
    return []
  }

  return (
    user?.teams?.reduce<Team['id'][]>((accumulator, { roles, team }) => {
      if (role && !roles.includes(role)) {
        return accumulator
      }

      if (team) {
        accumulator.push(extractId(team))
      }

      return accumulator
    }, []) || []
  )
}
