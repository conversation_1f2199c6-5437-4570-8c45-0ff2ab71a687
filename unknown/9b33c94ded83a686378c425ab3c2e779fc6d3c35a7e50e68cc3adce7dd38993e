import type { CollectionConfig } from 'payload'
import { isUserOrSuperAdmin } from '@/collections/access'
import { AdminCollectionsGroup, collections } from '@/constants'
import { isTeamOwnerOrSuperAdmin, readTeamAccess } from './access'

const {
  teams: { slug: teamsSlug }
} = collections

export const teams: CollectionConfig<typeof teamsSlug> = {
  slug: teamsSlug,
  access: {
    create: isUserOrSuperAdmin,
    read: readTeamAccess,
    update: isTeamOwnerOrSuperAdmin,
    delete: isTeamOwnerOrSuperAdmin
  },
  admin: {
    useAsTitle: 'name',
    group: AdminCollectionsGroup.application
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true
    },
    {
      name: 'image',
      type: 'upload',
      relationTo: 'media'
    },
    {
      name: 'user',
      type: 'relationship',
      relationTo: 'users',
      hasMany: false,
      required: true,
      admin: {
        position: 'sidebar'
      }
    }
  ],
  timestamps: true
}
