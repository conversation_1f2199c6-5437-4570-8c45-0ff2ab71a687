/**
 * API Client Runtime Configuration
 *
 * This file configures the Next.js API client using the Runtime API pattern.
 * It sets up base URLs, default headers, and other client configuration
 * that gets applied before the client is initialized.
 *
 * @see https://heyapi.dev/openapi-ts/clients/next-js#runtime-api
 */
import type { CreateClientConfig } from './api-sdk/client.gen'

export const createClientConfig: CreateClientConfig = (config) => {
  // Configure base URL for both server and client
  const baseUrl = process.env.NEXT_PUBLIC_SERVER_URL || 'https://serplens.local'

  return {
    ...config,
    baseUrl,
    headers: {
      'x-serplens': 'true',
      ...(config?.headers || {})
    }
  }
}
