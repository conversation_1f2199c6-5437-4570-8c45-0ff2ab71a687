import type { CollectionConfig } from 'payload'
import { isSuperAdminAccess } from '@/collections/access'
import { AdminCollectionsGroup, collections } from '@/constants'

const {
  subscriptions: { slug: subscriptionsSlug }
} = collections

export const subscriptions: CollectionConfig = {
  slug: subscriptionsSlug,
  access: {
    create: isSuperAdminAccess,
    read: isSuperAdminAccess,
    update: isSuperAdminAccess,
    delete: isSuperAdminAccess
  },
  admin: {
    useAsTitle: 'subscriptionId',
    group: AdminCollectionsGroup.application
  },
  fields: [
    {
      name: 'subscriptionId',
      type: 'text',
      required: true
    }
  ],
  timestamps: true
}
