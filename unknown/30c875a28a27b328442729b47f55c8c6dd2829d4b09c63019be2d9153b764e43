export const mapValuesAsync = async <T, U>(
  mapper: (value: T) => Promise<U>,
  record: Record<string, T>
): Promise<Record<string, U>> =>
  Object.fromEntries(
    await Promise.all(
      Object.entries(record).map(async ([key, value]) => [key, await mapper(value)])
    )
  )

export const visitObjectNodes = (
  subject: Record<string, unknown> | Array<unknown>,
  visitor: (
    subject: Record<string, unknown> & Array<unknown>,
    key: string | number,
    value: unknown
  ) => void
) => {
  if (Array.isArray(subject)) {
    for (const [index, value] of subject.entries()) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      visitor(subject as any, index, value)
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      visitObjectNodes(value as any, visitor)
    }
  } else if (typeof subject === 'object' && subject !== null && subject !== undefined) {
    for (const [key, value] of Object.entries(subject)) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      visitor(subject as any, key, value)
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      visitObjectNodes(value as any, visitor)
    }
  }
}
