import type { CollectionConfig } from 'payload'
import { isUserOrSuperAdmin } from '@/collections/access'
import { AdminCollectionsGroup, collections } from '@/constants'
import { isTeamOwnerOrSuperAdmin, readTeamAccess } from './access'

const {
  projects: { slug: projectsSlug }
} = collections

export const projects: CollectionConfig = {
  slug: projectsSlug,
  access: {
    create: isUserOrSuperAdmin,
    read: readTeamAccess,
    update: isTeamOwnerOrSuperAdmin,
    delete: isTeamOwnerOrSuperAdmin
  },
  admin: {
    useAsTitle: 'name',
    group: AdminCollectionsGroup.application
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true
    },
    {
      name: 'domain',
      type: 'text',
      required: true
    },
    {
      name: 'projectGoal',
      type: 'textarea'
    },
    {
      name: 'competitorsDomains',
      type: 'array',
      fields: [
        {
          name: 'domain',
          type: 'text',
          required: true
        }
      ]
    },
    {
      name: 'image',
      type: 'upload',
      relationTo: 'media'
    },
    {
      name: 'user',
      type: 'relationship',
      relationTo: 'users',
      hasMany: false,
      required: true,
      admin: {
        position: 'sidebar'
      }
    },
    {
      name: 'keywordsCount',
      type: 'number',
      defaultValue: 0
    }
  ],
  timestamps: true
}
