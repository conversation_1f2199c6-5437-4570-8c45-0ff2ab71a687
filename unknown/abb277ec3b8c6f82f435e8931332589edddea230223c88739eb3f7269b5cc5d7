import type { CollectionConfig } from 'payload'
import { authenticatedOrPublished, createUpdateDelete } from '@/collections/access'
import {
  archive,
  callToAction,
  content,
  formBlock,
  mediaBlock
} from '@/collections/blocks'
import { AdminCollectionsGroup, collections } from '@/constants'
import { slugField } from '@/fields/slug'
import { hero } from '@/heros'
import { generatePreviewPath } from '@/utils/server'
import {
  MetaDescriptionField,
  MetaImageField,
  MetaTitleField,
  OverviewField,
  PreviewField
} from '@payloadcms/plugin-seo/fields'
import { populatePublishedAt, revalidateDelete, revalidatePage } from './hooks'

const {
  pages: { slug: pagesSlug },
  media: { slug: mediaSlug }
} = collections

export const pages: CollectionConfig<typeof pagesSlug> = {
  slug: pagesSlug,
  access: {
    create: createUpdateDelete,
    delete: createUpdateDelete,
    read: authenticatedOrPublished,
    update: createUpdateDelete
  },
  defaultPopulate: {
    title: true,
    slug: true
  },
  admin: {
    defaultColumns: ['title', 'slug', 'updatedAt'],
    group: AdminCollectionsGroup.marketing,
    livePreview: {
      url: ({ data, req }) => {
        const path = generatePreviewPath({
          slug: typeof data?.slug === 'string' ? data.slug : '',
          collection: pagesSlug,
          req
        })

        return path
      }
    },
    preview: (data, { req }) =>
      generatePreviewPath({
        slug: typeof data?.slug === 'string' ? data.slug : '',
        collection: pagesSlug,
        req
      }),
    useAsTitle: 'title'
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true
    },
    {
      type: 'tabs',
      tabs: [
        {
          fields: [hero],
          label: 'Hero'
        },
        {
          fields: [
            {
              name: 'layout',
              type: 'blocks',
              blocks: [callToAction, content, mediaBlock, archive, formBlock],
              required: true,
              admin: {
                initCollapsed: true
              }
            }
          ],
          label: 'Content'
        },
        {
          name: 'meta',
          label: 'SEO',
          fields: [
            OverviewField({
              titlePath: 'meta.title',
              descriptionPath: 'meta.description',
              imagePath: 'meta.image'
            }),
            MetaTitleField({
              hasGenerateFn: true
            }),
            MetaImageField({
              relationTo: mediaSlug
            }),

            MetaDescriptionField({}),
            PreviewField({
              // if the `generateUrl` function is configured
              hasGenerateFn: true,

              // field paths to match the target field for data
              titlePath: 'meta.title',
              descriptionPath: 'meta.description'
            })
          ]
        }
      ]
    },
    {
      name: 'publishedAt',
      type: 'date',
      admin: {
        position: 'sidebar'
      }
    },
    ...slugField()
  ],
  hooks: {
    afterChange: [revalidatePage],
    beforeChange: [populatePublishedAt],
    afterDelete: [revalidateDelete]
  },
  versions: {
    drafts: {
      autosave: {
        interval: 100 // We set this interval for optimal live preview
      },
      schedulePublish: true
    },
    maxPerDoc: 50
  }
}
