import type { GlobalConfig } from 'payload'
import { anyone } from '@/collections/access'
import { AdminCollectionsGroup, collections } from '@/constants'
import { link } from '@/fields'
import { revalidateFooter } from './hooks'

const {
  footer: { slug: footerSlug }
} = collections

export const footer: GlobalConfig<typeof footerSlug> = {
  slug: footerSlug,
  access: {
    read: anyone
  },
  admin: {
    group: AdminCollectionsGroup.marketing
  },
  fields: [
    {
      name: 'navItems',
      type: 'array',
      fields: [
        link({
          appearances: false
        })
      ],
      maxRows: 6,
      admin: {
        initCollapsed: true,
        components: {
          RowLabel: '@/footer/RowLabel#RowLabel'
        }
      }
    }
  ],
  hooks: {
    afterChange: [revalidateFooter]
  }
}
