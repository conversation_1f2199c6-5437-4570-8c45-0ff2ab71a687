import type { CollectionAfterLoginHook } from 'payload'
import { generatePayloadCookie, mergeHeaders } from 'payload'

export const setMultiAccountsCookie: CollectionAfterLoginHook = async ({
  collection,
  user,
  token,
  req
}) => {
  const userAccountCookie = generatePayloadCookie({
    cookiePrefix: `user-${user.id}`,
    token: token,
    collectionAuthConfig: collection.auth
  })

  const newHeaders = new Headers({
    'Set-Cookie': userAccountCookie as string
  })

  req.responseHeaders = req.responseHeaders
    ? mergeHeaders(req.responseHeaders, newHeaders)
    : newHeaders

  return user
}
