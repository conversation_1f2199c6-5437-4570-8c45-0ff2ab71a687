import type { Access } from 'payload'
import { isSuperAdmin } from '@/access'
import { authenticated } from '@/collections/access'
import { Team, User } from '@/payload-types'
import { isTeamMemberAccess } from './isTeamMemberAccess'
import { isTeamOwner } from './isTeamOwner'

export const readTeamAccess: Access = ({ req, id }) => {
  if (!authenticated({ req })) return false
  if (
    isSuperAdmin(req.user) ||
    isTeamOwner({ id: id as Team['id'], user: req.user as User })
  ) {
    return true
  }

  return isTeamMemberAccess({ req })
}
