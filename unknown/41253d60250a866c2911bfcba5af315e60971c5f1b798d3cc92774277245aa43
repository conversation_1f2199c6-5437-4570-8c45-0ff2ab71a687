import type { PayloadRequest } from 'payload'
import { generateV30Spec } from '@/openapi/generators'
import type { SanitizedOpenAPIOptions } from '@/openapi/types'

export const openapiHandler = async (req: PayloadRequest): Promise<Response> => {
  try {
    // Configure OpenAPI generation options
    const openApiOptions: SanitizedOpenAPIOptions = {
      openapiVersion: '3.0',
      authEndpoint: 'users/login',
      metadata: {
        title: 'SERP Payload API',
        version: '1.0.0',
        description: 'Auto-generated API documentation for SERP Payload CMS'
      }
    }

    // Generate OpenAPI specification for all collections
    const spec = await generateV30Spec(req.payload, openApiOptions)

    // Return the OpenAPI specification as JSON Response
    return Response.json(spec)
  } catch (error) {
    console.error('Error generating OpenAPI specification:', error)

    return Response.json(
      {
        error: 'Failed to generate OpenAPI specification',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
