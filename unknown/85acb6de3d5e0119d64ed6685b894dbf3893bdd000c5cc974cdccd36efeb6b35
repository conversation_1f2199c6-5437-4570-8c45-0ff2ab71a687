import type { CollectionConfig } from 'payload'
import { anyone, createUpdateDelete } from '@/collections/access'
import { AdminCollectionsGroup, collections } from '@/constants'
import {
  FixedToolbarFeature,
  InlineToolbarFeature,
  lexicalEditor
} from '@payloadcms/richtext-lexical'

const {
  assets: { slug: assetsSlug }
} = collections

export const assets: CollectionConfig = {
  slug: assetsSlug,
  access: {
    create: createUpdateDelete,
    delete: createUpdateDelete,
    read: anyone,
    update: createUpdateDelete
  },
  admin: {
    group: AdminCollectionsGroup.application
  },
  fields: [
    {
      name: 'alt',
      type: 'text'
    },
    {
      name: 'caption',
      type: 'richText',
      editor: lexicalEditor({
        features: ({ rootFeatures }) => {
          return [...rootFeatures, FixedToolbarFeature(), InlineToolbarFeature()]
        }
      })
    }
  ],
  upload: {
    adminThumbnail: 'thumbnail',
    focalPoint: true,
    imageSizes: [
      {
        name: 'thumbnail',
        width: 300
      }
    ]
  }
}
