{"name": "serp-payload", "version": "1.0.0", "description": "Website template for Payload", "license": "MIT", "type": "module", "scripts": {"build": "cross-env NODE_OPTIONS=--no-deprecation pnpm run lint && next build", "dev": "cross-env pnpm run lint && node startDev.js", "postbuild": "next-sitemap --config next-sitemap.config.cjs", "dev:prod": "cross-env NODE_OPTIONS=--no-deprecation rm -rf .next && pnpm build && pnpm start", "generate:api": "openapi-ts", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "ii": "cross-env NODE_OPTIONS=--no-deprecation pnpm --ignore-workspace install", "lint": "cross-env NODE_OPTIONS=--no-deprecation next lint --fix --quiet && prettier --write --check .", "lint:fix": "cross-env NODE_OPTIONS=--no-deprecation next lint --fix", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "reinstall": "cross-env NODE_OPTIONS=--no-deprecation rm -rf node_modules && rm pnpm-lock.yaml && pnpm --ignore-workspace install", "start": "cross-env NODE_OPTIONS=--no-deprecation next start", "migration:create": "payload migration:create", "ci": "payload migrate && pnpm build"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@openapi-contrib/json-schema-to-openapi-schema": "3.0.3", "@payloadcms/admin-bar": "3.42.0", "@payloadcms/db-postgres": "3.42.0", "@payloadcms/db-vercel-postgres": "3.42.0", "@payloadcms/email-resend": "3.42.0", "@payloadcms/live-preview-react": "3.42.0", "@payloadcms/next": "3.42.0", "@payloadcms/payload-cloud": "3.42.0", "@payloadcms/plugin-form-builder": "3.42.0", "@payloadcms/plugin-multi-tenant": "3.42.0", "@payloadcms/plugin-nested-docs": "3.42.0", "@payloadcms/plugin-redirects": "3.42.0", "@payloadcms/plugin-search": "3.42.0", "@payloadcms/plugin-seo": "3.42.0", "@payloadcms/plugin-stripe": "^3.42.0", "@payloadcms/richtext-lexical": "3.42.0", "@payloadcms/storage-s3": "3.42.0", "@payloadcms/ui": "3.42.0", "@tanstack/react-query": "^5.80.6", "@tanstack/react-query-devtools": "^5.80.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cross-env": "^7.0.3", "geist": "^1.4.2", "graphql": "^16.11.0", "input-otp": "^1.4.2", "lucide-react": "^0.511.0", "mutative": "^1.2.0", "next": "15.3.2", "next-sitemap": "^4.2.3", "openapi-types": "^12.1.3", "payload": "3.42.0", "prism-react-renderer": "^2.4.1", "radix-ui": "latest", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "7.56.4", "sharp": "0.34.2", "tailwind-merge": "^3.3.0", "zod": "^3.25.28"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@hey-api/openapi-ts": "0.73.0", "@tailwindcss/postcss": "^4.1.7", "@tailwindcss/typography": "^0.5.16", "@tanstack/eslint-plugin-query": "^5.60.1", "@trivago/prettier-plugin-sort-imports": "4.3.0", "@types/escape-html": "^1.0.4", "@types/json-schema": "^7.0.15", "@types/node": "22.15.21", "@types/react": "19.1.4", "@types/react-dom": "19.1.5", "camelcase": "^8.0.0", "copyfiles": "^2.4.1", "eslint": "^9.27.0", "eslint-config-next": "15.3.2", "eslint-plugin-disable": "^2.0.3", "eslint-plugin-unicorn": "^59.0.1", "json-schema": "^0.4.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "tailwindcss": "4.1.7", "tw-animate-css": "^1.3.0", "typescript": "5.8.3"}, "engines": {"node": "^18.20.2 || >=20.9.0", "pnpm": "^9 || ^10", "npm": "please-use-pnpm"}, "pnpm": {"onlyBuiltDependencies": ["sharp"]}, "packageManager": "pnpm@10.11.0"}