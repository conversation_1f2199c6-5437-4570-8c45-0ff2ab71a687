[2025-06-23T16:17:31.341Z] Error: Missing $ref pointer "#/components/schemas/HeaderWrite". Token "HeaderWrite" does not exist.
Stack:
MissingPointerError: Missing $ref pointer "#/components/schemas/HeaderWrite". Token "HeaderWrite" does not exist.
    at Pointer.resolve (/Users/<USER>/data/projects/serp-payload/node_modules/.pnpm/@hey-api+json-schema-ref-parser@1.0.6/node_modules/@hey-api/json-schema-ref-parser/dist/lib/pointer.js:113:23)
    at $Ref.resolve (/Users/<USER>/data/projects/serp-payload/node_modules/.pnpm/@hey-api+json-schema-ref-parser@1.0.6/node_modules/@hey-api/json-schema-ref-parser/dist/lib/ref.js:79:24)
    at $Refs._resolve (/Users/<USER>/data/projects/serp-payload/node_modules/.pnpm/@hey-api+json-schema-ref-parser@1.0.6/node_modules/@hey-api/json-schema-ref-parser/dist/lib/refs.js:168:21)
    at inventory$Ref (/Users/<USER>/data/projects/serp-payload/node_modules/.pnpm/@hey-api+json-schema-ref-parser@1.0.6/node_modules/@hey-api/json-schema-ref-parser/dist/lib/bundle.js:66:27)
    at crawl (/Users/<USER>/data/projects/serp-payload/node_modules/.pnpm/@hey-api+json-schema-ref-parser@1.0.6/node_modules/@hey-api/json-schema-ref-parser/dist/lib/bundle.js:157:21)
    at crawl (/Users/<USER>/data/projects/serp-payload/node_modules/.pnpm/@hey-api+json-schema-ref-parser@1.0.6/node_modules/@hey-api/json-schema-ref-parser/dist/lib/bundle.js:169:21)
    at crawl (/Users/<USER>/data/projects/serp-payload/node_modules/.pnpm/@hey-api+json-schema-ref-parser@1.0.6/node_modules/@hey-api/json-schema-ref-parser/dist/lib/bundle.js:169:21)
    at crawl (/Users/<USER>/data/projects/serp-payload/node_modules/.pnpm/@hey-api+json-schema-ref-parser@1.0.6/node_modules/@hey-api/json-schema-ref-parser/dist/lib/bundle.js:169:21)
    at crawl (/Users/<USER>/data/projects/serp-payload/node_modules/.pnpm/@hey-api+json-schema-ref-parser@1.0.6/node_modules/@hey-api/json-schema-ref-parser/dist/lib/bundle.js:169:21)
    at crawl (/Users/<USER>/data/projects/serp-payload/node_modules/.pnpm/@hey-api+json-schema-ref-parser@1.0.6/node_modules/@hey-api/json-schema-ref-parser/dist/lib/bundle.js:169:21)
    at bundle (/Users/<USER>/data/projects/serp-payload/node_modules/.pnpm/@hey-api+json-schema-ref-parser@1.0.6/node_modules/@hey-api/json-schema-ref-parser/dist/lib/bundle.js:313:5)
    at $RefParser.bundle (/Users/<USER>/data/projects/serp-payload/node_modules/.pnpm/@hey-api+json-schema-ref-parser@1.0.6/node_modules/@hey-api/json-schema-ref-parser/dist/lib/index.js:133:32)
    at async ea (/Users/<USER>/data/projects/serp-payload/node_modules/.pnpm/@hey-api+openapi-ts@0.73.0_typescript@5.8.3/node_modules/@hey-api/openapi-ts/dist/index.cjs:12:1892)
    at async Ys (/Users/<USER>/data/projects/serp-payload/node_modules/.pnpm/@hey-api+openapi-ts@0.73.0_typescript@5.8.3/node_modules/@hey-api/openapi-ts/dist/index.cjs:38:121903)
    at async Promise.all (index 0)
    at async d$ (/Users/<USER>/data/projects/serp-payload/node_modules/.pnpm/@hey-api+openapi-ts@0.73.0_typescript@5.8.3/node_modules/@hey-api/openapi-ts/dist/index.cjs:1338:4627)
    at async Module.register (webpack-internal:///(instrument)/./src/instrumentation.js:16:13)
    at async Span.traceAsyncFn (/Users/<USER>/data/projects/serp-payload/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)
    at async DevServer.runInstrumentationHookIfAvailable (/Users/<USER>/data/projects/serp-payload/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:486:9)
    at async DevServer.prepareImpl (/Users/<USER>/data/projects/serp-payload/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:544:9)
    at async DevServer.prepareImpl (/Users/<USER>/data/projects/serp-payload/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:219:9)
    at async NextServer.prepare (/Users/<USER>/data/projects/serp-payload/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next.js:160:13)
    at async initializeImpl (/Users/<USER>/data/projects/serp-payload/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/render-server.js:105:5)
    at async initialize (/Users/<USER>/data/projects/serp-payload/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:504:22)
    at async Server.<anonymous> (/Users/<USER>/data/projects/serp-payload/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:304:36)
