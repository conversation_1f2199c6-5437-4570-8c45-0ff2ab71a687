import { withPayload } from '@payloadcms/next/withPayload'
import redirects from './redirects.js'

const NEXT_PUBLIC_SERVER_URL = process.env.VERCEL_PROJECT_PRODUCTION_URL
  ? `https://${process.env.VERCEL_PROJECT_PRODUCTION_URL}`
  : undefined || process.env.NEXT_PUBLIC_SERVER_URL || 'https://serplens.local'

const doEndpoint = process.env.S3_ENDPOINT

const policies = {
  'default-src': ["'self'", NEXT_PUBLIC_SERVER_URL],
  'script-src': [
    "'self'",
    NEXT_PUBLIC_SERVER_URL,
    "'unsafe-inline'",
    "'unsafe-eval'",
    'https://maps.googleapis.com',
    'https://vercel.live',
    'https://cdn.jsdelivr.net'
  ],
  'child-src': ["'self'", NEXT_PUBLIC_SERVER_URL],
  'style-src': [
    "'self'",
    NEXT_PUBLIC_SERVER_URL,
    "'unsafe-inline'",
    'https://fonts.googleapis.com',
    'https://cdn.jsdelivr.net'
  ],
  'img-src': [
    "'self'",
    NEXT_PUBLIC_SERVER_URL,
    'https://www.gravatar.com',
    'https://raw.githubusercontent.com',
    ...(doEndpoint ? [doEndpoint] : []),
    'blob:',
    'data:'
  ],
  'font-src': ["'self'", NEXT_PUBLIC_SERVER_URL, 'https://fonts.gstatic.com'],
  'frame-src': ["'self'", NEXT_PUBLIC_SERVER_URL, 'https://vercel.live'],
  'connect-src': ["'self'", NEXT_PUBLIC_SERVER_URL, 'https://maps.googleapis.com']
}

const csp = Object.entries(policies)
  .map(([key, value]) => {
    if (Array.isArray(value)) {
      return `${key} ${value.join(' ')}`
    }
    return ''
  })
  .join('; ')

const headers = async () => {
  const headers = [
    {
      source: '/(.*)',
      headers: [
        {
          key: 'Content-Security-Policy',
          value: csp
        }
      ]
    }
  ]

  return headers
}

/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      ...[NEXT_PUBLIC_SERVER_URL /* 'https://example.com' */].map((item) => {
        const url = new URL(item)

        return {
          hostname: url.hostname,
          protocol: url.protocol.replace(':', '')
        }
      })
    ]
  },
  reactStrictMode: true,
  redirects,
  headers,
  poweredByHeader: false
}

export default withPayload(nextConfig, { devBundleServerPackages: false })
