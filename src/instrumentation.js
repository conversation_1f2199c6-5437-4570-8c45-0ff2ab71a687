export async function register() {
  // Don't try to convert this to a var (https://stackoverflow.com/questions/77427874/next-js-instrumentation-behaves-differently-with-conditional-extracted-to-variab)
  if (process.env.NEXT_RUNTIME === 'nodejs') {
    // Reminder don't try to use checkEnv. See above
    if (process.env.NODE_ENV === 'development') {
      // Generate our OpenAPI 3 JSON file and API client using our new generators
      const { generateApi } = await import('@/scripts/generateApi')
      const openApiPath = await generateApi()

      // Generate API client using Hey API
      const { createClient } = require('@hey-api/openapi-ts') // eslint-disable-line @typescript-eslint/no-require-imports
      await createClient({
        input: openApiPath,
        output: {
          format: 'prettier',
          lint: 'eslint',
          path: './src/lib/api-sdk'
        },
        plugins: [
          {
            name: '@hey-api/client-next',
            runtimeConfigPath: './src/lib/clientConfig'
          },
          '@tanstack/react-query'
        ]
      })
    }
  }
}
