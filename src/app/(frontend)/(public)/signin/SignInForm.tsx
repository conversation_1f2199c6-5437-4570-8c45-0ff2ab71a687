'use client';

import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button, Form, FormControl, FormField, FormItem, FormLabel, FormMessage, Input, Spinner } from '@/components/ui';
import { useAuth } from '@/hooks';
import { zodResolver } from '@hookform/resolvers/zod';


const formSchema = z.object({
  email: z
    .string()
    .nonempty({ message: 'Email is required.' })
    .email({ message: 'Please enter a valid email address.' }),
  password: z.string().nonempty({ message: 'Password is required.' })
})

export function LoginForm() {
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: ''
    }
  })

  const { requestOtpMutation, isRequestingOtp } = useAuth()

  function onSubmit(data: z.infer<typeof formSchema>) {
    requestOtpMutation(data)
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className='sm:space-y-6 space-y-4'>
        <FormField
          control={form.control}
          name='email'
          render={({ field }) => (
            <FormItem>
              <FormLabel className='sr-only'>Email</FormLabel>
              <FormControl>
                <Input
                  type='email'
                  placeholder='<EMAIL>'
                  className='border-border p-3 leading-6 h-11.5 bg-primary-foreground'
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button
          disabled={isRequestingOtp}
          variant='primary'
          size='lg'
          type='submit'
          className='w-full h-11.5 cursor-pointer flex items-center gap-2'>
          {isRequestingOtp ? <Spinner className='mr-2' /> : 'Sign In With Email'}
        </Button>
      </form>
    </Form>
  )
}