import type { JSONSchema4 } from 'json-schema'
import type { OpenAPIV3_1 } from 'openapi-types'
import type { Collection } from 'payload'
import { composeRef } from './generators'
import { collectionName, componentName } from './naming'
import { apiKeySecurity } from './securitySchemes'

// Helper function to capitalize endpoint names
const capitalize = (string_: string): string => {
  return string_.charAt(0).toUpperCase() + string_.slice(1)
}

// Type definition for custom endpoint
interface CustomEndpointDefinition {
  method: 'get' | 'post' | 'put' | 'patch' | 'delete'
  path: string // Full API path including /api prefix (e.g., '/api/users/otp/request')
  summary: string
  description?: string
  requestSchema?: JSONSchema4
  responseSchema?: JSONSchema4
  errorResponses?: Record<number, string>
  security?: boolean
}

// Static definitions for custom endpoints
// Note: paths should include full API path (e.g., '/api/users/otp/request')
const customEndpointDefinitions: Record<
  string,
  Record<string, CustomEndpointDefinition>
> = {
  users: {
    otpRequest: {
      method: 'post',
      path: '/api/users/otp/request',
      summary: 'Request OTP for login',
      description: 'Send OTP to user email for authentication',
      requestSchema: {
        type: 'object',
        properties: {
          email: { type: 'string', format: 'email' }
        },
        required: ['email'],
        title: 'User OTP request'
      } as JSONSchema4,
      responseSchema: {
        type: 'object',
        properties: {
          type: { type: 'string', example: 'Email' },
          message: { type: 'string', example: 'Successfully sent one-time password.' },
          value: { type: 'string', format: 'email' }
        },
        required: ['type', 'message', 'value'],
        title: 'User OTP request response'
      } as JSONSchema4,
      errorResponses: {
        400: 'Invalid email format or missing email',
        500: 'Failed to send OTP email'
      },
      security: false // No security required for requesting OTP (like login)
    },
    otpLogin: {
      method: 'post',
      path: '/api/users/otp/login',
      summary: 'Login with OTP',
      description: 'Authenticate user using email and OTP',
      requestSchema: {
        type: 'object',
        properties: {
          email: { type: 'string', format: 'email' },
          otp: { type: 'string', minLength: 6, maxLength: 6 }
        },
        required: ['email', 'otp'],
        title: 'User OTP login request'
      } as JSONSchema4,
      responseSchema: {
        type: 'object',
        properties: {
          user: { $ref: '#/components/schemas/User' }, // Reference to existing User schema
          token: { type: 'string' },
          exp: { type: 'number' }
        },
        required: ['user', 'token', 'exp'],
        title: 'User OTP login response'
      } as JSONSchema4,
      errorResponses: {
        401: 'Invalid OTP or expired',
        404: 'User not found'
      },
      security: false // No security required for login
    },
    refreshToken: {
      method: 'post',
      path: '/api/users/refresh-token',
      summary: 'Refresh authentication token',
      description: 'Get new access token using refresh token',
      responseSchema: {
        type: 'object',
        properties: {
          user: { $ref: '#/components/schemas/User' },
          token: { type: 'string' },
          exp: { type: 'number' }
        },
        required: ['user', 'token', 'exp'],
        title: 'User refresh token response'
      } as JSONSchema4,
      errorResponses: {
        401: 'Invalid or expired refresh token'
      },
      security: true
    }
  }
}

// Generator functions following exact same patterns as auth functions
export const generateCustomEndpointRequestSchemas = (
  collection: Collection
): Record<string, JSONSchema4> => {
  const { singular } = collectionName(collection)
  const { slug } = collection.config
  const schemas: Record<string, JSONSchema4> = {}

  const endpoints = customEndpointDefinitions[slug]
  if (!endpoints) return schemas

  Object.entries(endpoints).forEach(([endpointName, definition]) => {
    if (definition.requestSchema) {
      // Follow exact same pattern as generateAuthRequestSchemas
      schemas[
        componentName('schemas', singular, {
          suffix: `${capitalize(endpointName)}Request`
        })
      ] = {
        ...definition.requestSchema,
        title: definition.requestSchema.title || `${singular} ${endpointName} request`
      }
    }
  })

  return schemas
}

export const generateCustomEndpointResponseSchemas = (
  collection: Collection
): Record<string, JSONSchema4> => {
  const { singular } = collectionName(collection)
  const { slug } = collection.config
  const schemas: Record<string, JSONSchema4> = {}

  const endpoints = customEndpointDefinitions[slug]
  if (!endpoints) return schemas

  Object.entries(endpoints).forEach(([endpointName, definition]) => {
    if (definition.responseSchema) {
      // Follow exact same pattern as generateAuthResponseSchemas
      schemas[
        componentName('schemas', singular, {
          suffix: `${capitalize(endpointName)}Response`
        })
      ] = {
        ...definition.responseSchema,
        title: definition.responseSchema.title || `${singular} ${endpointName} response`
      }
    }
  })

  return schemas
}

export const generateCustomEndpointRequestBodies = (
  collection: Collection
): Record<string, OpenAPIV3_1.RequestBodyObject> => {
  const { singular } = collectionName(collection)
  const { slug } = collection.config
  const requestBodies: Record<string, OpenAPIV3_1.RequestBodyObject> = {}

  const endpoints = customEndpointDefinitions[slug]
  if (!endpoints) return requestBodies

  Object.entries(endpoints).forEach(([endpointName, definition]) => {
    if (definition.requestSchema) {
      // Follow exact same pattern as generateAuthRequestBodies
      requestBodies[
        componentName('requestBodies', singular, { suffix: capitalize(endpointName) })
      ] = {
        description: definition.description || `${singular} ${endpointName} request`,
        content: {
          'application/json': {
            schema: composeRef('schemas', singular, {
              suffix: `${capitalize(endpointName)}Request`
            })
          }
        }
      }
    }
  })

  return requestBodies
}

export const generateCustomEndpointResponses = (
  collection: Collection
): Record<string, OpenAPIV3_1.ResponseObject> => {
  const { singular } = collectionName(collection)
  const { slug } = collection.config
  const responses: Record<string, OpenAPIV3_1.ResponseObject> = {}

  const endpoints = customEndpointDefinitions[slug]
  if (!endpoints) return responses

  Object.entries(endpoints).forEach(([endpointName, definition]) => {
    if (definition.responseSchema) {
      // Follow exact same pattern as generateAuthResponses
      responses[
        componentName('responses', singular, { suffix: capitalize(endpointName) })
      ] = {
        description: `${singular} ${endpointName} response`,
        content: {
          'application/json': {
            schema: composeRef('schemas', singular, {
              suffix: `${capitalize(endpointName)}Response`
            })
          }
        }
      }
    }
  })

  return responses
}

export const generateCustomEndpointOperations = async (
  collection: Collection
): Promise<Record<string, OpenAPIV3_1.PathItemObject>> => {
  const { singular, plural } = collectionName(collection)
  const { slug } = collection.config
  const operations: Record<string, OpenAPIV3_1.PathItemObject> = {}

  const endpoints = customEndpointDefinitions[slug]
  if (!endpoints) return operations

  Object.entries(endpoints).forEach(([endpointName, definition]) => {
    const fullPath = definition.path

    const operationResponses: Record<
      string,
      OpenAPIV3_1.ReferenceObject | OpenAPIV3_1.ResponseObject
    > = {}

    // Add success response if exists
    if (definition.responseSchema) {
      // Use 201 for POST operations to match Payload's pattern and ensure mutation-only generation
      const successStatus = definition.method === 'post' ? '201' : '200'
      operationResponses[successStatus] = composeRef('responses', singular, {
        suffix: capitalize(endpointName)
      })
    }

    // Add error responses
    if (definition.errorResponses) {
      Object.entries(definition.errorResponses).forEach(([status, description]) => {
        operationResponses[status] = { description: description as string }
      })
    }

    operations[fullPath] = {
      [definition.method]: {
        summary: definition.summary,
        description: definition.description,
        tags: [plural],
        ...(definition.requestSchema && {
          requestBody: composeRef('requestBodies', singular, {
            suffix: capitalize(endpointName)
          })
        }),
        responses: operationResponses,
        ...(definition.security !== false && { security: [apiKeySecurity] })
      }
    }
  })

  return operations
}

// Helper function to check if collection has custom endpoints
export const hasCustomEndpoints = (collection: Collection): boolean => {
  return customEndpointDefinitions[collection.config.slug] !== undefined
}
