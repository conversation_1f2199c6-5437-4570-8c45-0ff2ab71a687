import type { JSONSchema4 } from 'json-schema'
import { create } from 'mutative'
import type { OpenAPIV3, OpenAPIV3_1 } from 'openapi-types'
import type {
  Access,
  AccessArgs,
  Collection,
  Field,
  FieldBase,
  PayloadRequest,
  RadioField,
  SanitizedCollectionConfig,
  SanitizedConfig,
  SanitizedGlobalConfig,
  SelectField
} from 'payload'
import { entityToJSONSchema } from 'payload'
import _jsonSchemaToOpenapiSchema from '@openapi-contrib/json-schema-to-openapi-schema'
import {
  generateCustomEndpointOperations,
  generateCustomEndpointRequestBodies,
  generateCustomEndpointRequestSchemas,
  generateCustomEndpointResponseSchemas,
  generateCustomEndpointResponses,
  hasCustomEndpoints
} from './customEndpoints'
import { type ComponentType, collectionName, componentName, globalName } from './naming'
import { apiKeySecurity, generateSecuritySchemes } from './securitySchemes'
import type { SanitizedOpenAPIOptions } from './types'
import { mapValuesAsync, visitObjectNodes } from './utils/objects'

// Configuration for which collections to include in OpenAPI generation
const OPENAPI_INCLUDED_COLLECTIONS = ['users', 'media', 'teams'] as const

// Configuration for which globals to include in OpenAPI generation
const OPENAPI_INCLUDED_GLOBALS = [] as const

// Filter functions
const shouldIncludeCollection = (collection: Collection): boolean => {
  return (OPENAPI_INCLUDED_COLLECTIONS as readonly string[]).includes(
    collection.config.slug
  )
}

const shouldIncludeGlobal = (global: SanitizedGlobalConfig): boolean => {
  return (OPENAPI_INCLUDED_GLOBALS as readonly string[]).includes(global.slug)
}

const baseQueryParams: Array<OpenAPIV3.ParameterObject & OpenAPIV3_1.ParameterObject> = [
  { in: 'query', name: 'depth', schema: { type: 'number' } },
  { in: 'query', name: 'locale', schema: { type: 'string' } },
  { in: 'query', name: 'fallback-locale', schema: { type: 'string' } }
]

async function jsonSchemaToOpenapiSchema(
  schema: JSONSchema4
): Promise<OpenAPIV3.Document> {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return await (_jsonSchemaToOpenapiSchema as any)(schema)
}

const adjustRefTargets = (
  payload: PayloadRequest['payload'],
  spec: Record<string, unknown>
): void => {
  const search = /^#\/definitions\/(.*)/

  visitObjectNodes(spec, (subject, key, value) => {
    const isRef = key === '$ref' && typeof value === 'string'

    if (!isRef) {
      return
    }

    subject[key] = value.replace(search, (_match, name: string) => {
      if (name === 'supportedTimezones') {
        return '#/components/schemas/supportedTimezones'
      }

      const collection = payload.collections[name as keyof typeof payload.collections]
      if (collection !== undefined) {
        name = collectionName(collection).singular
        return `#/components/schemas/${componentName('schemas', name)}`
      }

      const global = payload.globals.config.find(({ slug }) => slug === name)
      if (global !== undefined) {
        return `#/components/schemas/${componentName('schemas', globalName(global))}`
      }

      throw new Error(`Unknown reference: ${name}`)
    })
  })
}

const removeInterfaceNames = (target: SanitizedCollectionConfig) =>
  create(target, (draft) =>
    visitObjectNodes(draft, (subject, key) => {
      if (key === 'interfaceName') {
        subject[key] = undefined
      }
    })
  )

export const composeRef = (
  type: ComponentType,
  name: string,
  options?: { suffix?: string; prefix?: string }
): OpenAPIV3_1.ReferenceObject & OpenAPIV3.ReferenceObject => ({
  $ref: `#/components/${type}/${componentName(type, name, options)}`
})

const generateSchemaObject = (
  config: SanitizedConfig,
  collection: Collection
): JSONSchema4 => {
  const schema = entityToJSONSchema(
    config,
    removeInterfaceNames(collection.config), // the `interfaceName` option causes `entityToJSONSchema` to add a reference to a non-existing schema
    new Map(),
    'text',
    undefined
  )
  return {
    ...schema,
    title: collectionName(collection).singular
  }
}

const requestBodySchema = (fields: Array<Field>, schema: JSONSchema4): JSONSchema4 => ({
  ...schema,
  properties: Object.fromEntries(
    Object.entries(schema.properties ?? {})
      .filter(([slug]) => !['id', 'createdAt', 'updatedAt'].includes(slug))
      .map(([fieldName, schema]) => {
        const field = fields.find((field) => (field as FieldBase).name === fieldName)
        if (field?.type === 'relationship') {
          const target = Array.isArray(field.relationTo)
            ? field.relationTo
            : [field.relationTo]
          return [
            fieldName,
            { type: 'string', description: `ID of the ${target.join('/')}` }
          ]
        }

        return [fieldName, schema]
      })
  )
})

const generateRequestBodySchema = (
  config: SanitizedConfig,
  collection: Collection
): OpenAPIV3_1.RequestBodyObject => {
  const schema = entityToJSONSchema(
    config,
    removeInterfaceNames(collection.config), // the `interfaceName` option causes `entityToJSONSchema` to add a reference to a non-existing schema
    new Map(),
    'text',
    undefined
  )
  return {
    description: collectionName(collection).singular,
    content: {
      'application/json': {
        schema: requestBodySchema(
          collection.config.fields,
          schema
        ) as OpenAPIV3_1.SchemaObject
      }
    }
  }
}

const generateQueryOperationSchemas = (
  collection: Collection
): Record<string, JSONSchema4> => {
  const { singular } = collectionName(collection)

  return {
    [componentName('schemas', singular, { suffix: 'QueryOperations' })]: {
      title: `${singular} query operations`,
      type: 'object',
      properties: Object.fromEntries(
        (
          collection.config.fields.filter(({ type }) =>
            ['number', 'text', 'email', 'date', 'radio', 'checkbox', 'select'].includes(
              type
            )
          ) as Array<
            FieldBase & {
              type: 'number' | 'text' | 'email' | 'date' | 'radio' | 'select' | 'checkbox'
            }
          >
        ).map((field) => {
          const comparedValueSchema = (() => {
            switch (field.type) {
              case 'number':
                return { type: 'number' } as const
              case 'text':
                return { type: 'string' } as const
              case 'email':
                return { type: 'string', format: 'email' } as const
              case 'date':
                return { type: 'string', format: 'date-time' } as const
              case 'checkbox':
                return { type: 'boolean' } as const
              case 'radio':
              case 'select':
                return {
                  type: 'string',
                  enum: (field as RadioField | SelectField).options.map((it) =>
                    typeof it === 'string' ? it : it.value
                  )
                } as const
            }
          })()

          const properties: Record<string, JSONSchema4> = {
            ['equals']: comparedValueSchema,
            ['not_equals']: comparedValueSchema,
            ['in']: { type: 'string' },
            ['not_in']: { type: 'string' }
          }

          if (field.type === 'text') {
            properties['like'] = comparedValueSchema
          }

          if (field.type === 'text' || field.type === 'email') {
            properties['contains'] = comparedValueSchema
          }

          if (field.type === 'number' || field.type === 'date') {
            properties['greater_than'] = comparedValueSchema
            properties['greater_than_equal'] = comparedValueSchema
            properties['less_than'] = comparedValueSchema
            properties['less_than_equal'] = comparedValueSchema
          }

          return [
            field.name,
            {
              type: 'object',
              properties
            }
          ]
        })
      )
    },
    [componentName('schemas', singular, { suffix: 'QueryOperationsAnd' })]: {
      title: `${singular} query conjunction`,
      type: 'object',
      properties: {
        and: {
          type: 'array',
          items: {
            anyOf: [
              composeRef('schemas', singular, { suffix: 'QueryOperations' }),
              composeRef('schemas', singular, { suffix: 'QueryOperationsAnd' }),
              composeRef('schemas', singular, { suffix: 'QueryOperationsOr' })
            ]
          }
        }
      },
      required: ['and']
    },

    [componentName('schemas', singular, { suffix: 'QueryOperationsOr' })]: {
      title: `${singular} query disjunction`,
      type: 'object',
      properties: {
        or: {
          type: 'array',
          items: {
            anyOf: [
              composeRef('schemas', singular, { suffix: 'QueryOperations' }),
              composeRef('schemas', singular, { suffix: 'QueryOperationsAnd' }),
              composeRef('schemas', singular, { suffix: 'QueryOperationsOr' })
            ]
          }
        }
      },
      required: ['or']
    }
  }
}

const generateCollectionResponses = (
  collection: Collection
): Record<string, OpenAPIV3_1.ResponseObject & OpenAPIV3.ResponseObject> => {
  const { singular, plural } = collectionName(collection)

  return {
    [componentName('responses', singular)]: {
      description: `${singular} object`,
      content: {
        'application/json': {
          schema: composeRef('schemas', singular)
        }
      }
    },
    [componentName('responses', singular, { prefix: 'New' })]: {
      description: `${singular} object`,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              message: { type: 'string' },
              doc: {
                allOf: [
                  composeRef('schemas', singular),
                  {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      createdAt: {
                        type: 'string',
                        format: 'date-time'
                      },
                      updatedAt: {
                        type: 'string',
                        format: 'date-time'
                      }
                    },
                    required: ['id', 'createdAt', 'updatedAt']
                  }
                ]
              }
            },
            required: ['message', 'doc']
          }
        }
      }
    },
    [componentName('responses', singular, { suffix: 'NotFound' })]: {
      description: `${singular} not found`
    },
    [componentName('responses', singular, { suffix: 'List' })]: {
      description: `List of ${plural}`,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              docs: {
                type: 'array',
                items: composeRef('schemas', singular)
              },
              totalDocs: { type: 'integer' },
              limit: { type: 'integer' },
              totalPages: { type: 'integer' },
              page: { type: 'integer' },
              pagingCounter: { type: 'integer' },
              hasPrevPage: { type: 'boolean' },
              hasNextPage: { type: 'boolean' },
              prevPage: { type: ['integer', 'null'] },
              nextPage: { type: ['integer', 'null'] }
            },
            required: [
              'docs',
              'totalDocs',
              'limit',
              'totalPages',
              'page',
              'pagingCounter',
              'hasPrevPage',
              'hasNextPage',
              'prevPage',
              'nextPage'
            ]
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
          } as OpenAPIV3_1.NonArraySchemaObject as any
        }
      }
    }
  }
}

const isOpenToPublic = async (checker: Access): Promise<boolean> => {
  try {
    const result = await checker(
      new Proxy({} as unknown as AccessArgs, {
        get(target, p, receiver) {
          if (p === 'req') {
            throw new Error()
          }
          return Reflect.get(target, p, receiver)
        }
      })
    )
    return result === true
  } catch {
    return false
  }
}

const generateCollectionOperations = async (
  collection: Collection
): Promise<Record<string, OpenAPIV3.PathItemObject & OpenAPIV3_1.PathItemObject>> => {
  const { slug } = collection.config
  const { singular, plural } = collectionName(collection)
  const tags = [plural]

  const singleObjectResponses = {
    200: composeRef('responses', singular),
    404: composeRef('responses', singular, { suffix: 'NotFound' })
  } satisfies OpenAPIV3_1.ResponsesObject & OpenAPIV3.ResponsesObject

  return {
    [`/api/${slug}`]: {
      get: {
        summary: `Retrieve a list of ${plural}`,
        tags,
        parameters: [
          { in: 'query', name: 'page', schema: { type: 'number' } },
          { in: 'query', name: 'limit', schema: { type: 'number' } },
          ...baseQueryParams,
          {
            in: 'query',
            name: 'sort',
            schema: {
              type: 'string',
              enum: collection.config.fields.flatMap((field) => {
                if (
                  field.type === 'number' ||
                  field.type === 'text' ||
                  field.type === 'email' ||
                  field.type === 'date'
                ) {
                  return [field.name, `-${field.name}`]
                }
                return []
              })
            }
          },
          {
            in: 'query',
            name: 'where',
            style: 'deepObject',
            schema: {
              allOf: [
                { type: 'object' },
                {
                  anyOf: [
                    composeRef('schemas', singular, { suffix: 'QueryOperations' }),
                    composeRef('schemas', singular, { suffix: 'QueryOperationsAnd' }),
                    composeRef('schemas', singular, { suffix: 'QueryOperationsOr' })
                  ]
                }
              ]
            }
          }
        ],
        responses: {
          200: composeRef('responses', singular, { suffix: 'List' })
        },
        security: (await isOpenToPublic(collection.config.access.read))
          ? []
          : [apiKeySecurity]
      },
      post: {
        summary: `Create a new ${singular}`,
        tags,
        requestBody: composeRef('requestBodies', singular),
        responses: {
          201: composeRef('responses', singular, { prefix: 'New' })
        },
        security: (await isOpenToPublic(collection.config.access.create))
          ? []
          : [apiKeySecurity]
      }
    },
    [`/api/${slug}/{id}`]: {
      parameters: [
        ...baseQueryParams,
        {
          in: 'path',
          name: 'id',
          description: `ID of the ${singular}`,
          required: true,
          schema: {
            type: 'string'
          }
        }
      ],
      get: {
        summary: `Find a ${singular} by ID`,
        tags,
        responses: singleObjectResponses,
        security: (await isOpenToPublic(collection.config.access.read))
          ? []
          : [apiKeySecurity]
      },
      patch: {
        summary: `Update a ${singular}`,
        tags,
        responses: singleObjectResponses,
        security: (await isOpenToPublic(collection.config.access.update))
          ? []
          : [apiKeySecurity]
      },
      delete: {
        summary: `Delete a ${singular}`,
        tags,
        responses: singleObjectResponses,
        security: (await isOpenToPublic(collection.config.access.delete))
          ? []
          : [apiKeySecurity]
      }
    }
  }
}

const generateGlobalResponse = (
  global: SanitizedGlobalConfig
): OpenAPIV3_1.ResponseObject & OpenAPIV3.ResponseObject => {
  const name = globalName(global)

  return {
    description: name,
    content: {
      'application/json': {
        schema: composeRef('schemas', name, { suffix: 'Read' })
      }
    }
  }
}

const generateGlobalRequestBody = (
  global: SanitizedGlobalConfig
): OpenAPIV3_1.RequestBodyObject & OpenAPIV3.RequestBodyObject => {
  const name = globalName(global)

  return {
    description: name,
    content: {
      'application/json': {
        schema: composeRef('schemas', name, { suffix: 'Write' })
      }
    }
  }
}

const generateGlobalSchemas = (
  config: SanitizedConfig,
  global: SanitizedGlobalConfig
): Record<string, JSONSchema4> => {
  const schema = entityToJSONSchema(config, global, new Map(), 'text', undefined)

  return {
    [componentName('schemas', globalName(global))]: {
      ...schema,
      title: globalName(global)
    },
    [componentName('schemas', globalName(global), { suffix: 'Read' })]: {
      title: `${globalName(global)} (if present)`,
      oneOf: [schema, { type: 'object', properties: {} }]
    },
    [componentName('schemas', globalName(global), { suffix: 'Write' })]: {
      ...requestBodySchema(global.fields, schema),
      title: `${globalName(global)} (writable fields)`
    }
  }
}

const generateAuthRequestSchemas = (
  collection: Collection
): Record<string, JSONSchema4> => {
  const { singular } = collectionName(collection)

  return {
    [componentName('schemas', singular, { suffix: 'LoginRequest' })]: {
      type: 'object',
      properties: {
        email: { type: 'string', format: 'email' },
        password: { type: 'string' }
      },
      required: ['email', 'password'],
      title: `${singular} login request`
    },
    [componentName('schemas', singular, { suffix: 'ForgotPasswordRequest' })]: {
      type: 'object',
      properties: {
        email: { type: 'string', format: 'email' }
      },
      required: ['email'],
      title: `${singular} forgot password request`
    },
    [componentName('schemas', singular, { suffix: 'ResetPasswordRequest' })]: {
      type: 'object',
      properties: {
        token: { type: 'string' },
        password: { type: 'string' }
      },
      required: ['token', 'password'],
      title: `${singular} reset password request`
    },
    [componentName('schemas', singular, { suffix: 'UnlockRequest' })]: {
      type: 'object',
      properties: {
        email: { type: 'string', format: 'email' }
      },
      required: ['email'],
      title: `${singular} unlock request`
    }
  }
}

const generateAuthResponseSchemas = (
  collection: Collection
): Record<string, JSONSchema4> => {
  const { singular } = collectionName(collection)

  return {
    [componentName('schemas', singular, { suffix: 'LoginResponse' })]: {
      type: 'object',
      properties: {
        user: composeRef('schemas', singular),
        token: { type: 'string' },
        exp: { type: 'number' }
      },
      required: ['user', 'token', 'exp'],
      title: `${singular} login response`
    },
    [componentName('schemas', singular, { suffix: 'RefreshTokenResponse' })]: {
      type: 'object',
      properties: {
        user: composeRef('schemas', singular),
        token: { type: 'string' },
        exp: { type: 'number' }
      },
      required: ['user', 'token', 'exp'],
      title: `${singular} refresh token response`
    },
    [componentName('schemas', singular, { suffix: 'MeResponse' })]: {
      type: 'object',
      properties: {
        user: composeRef('schemas', singular),
        collection: { type: 'string' },
        strategy: { type: 'string' },
        exp: { type: 'number' },
        token: { type: 'string' },
        message: { type: 'string' }
      },
      required: ['user', 'collection', 'strategy', 'exp', 'token'],
      title: `${singular} me response`
    },
    [componentName('schemas', singular, { suffix: 'LogoutResponse' })]: {
      type: 'object',
      properties: {
        message: { type: 'string' }
      },
      required: ['message'],
      title: `${singular} logout response`
    },
    [componentName('schemas', singular, { suffix: 'ForgotPasswordResponse' })]: {
      type: 'object',
      properties: {
        message: { type: 'string' }
      },
      required: ['message'],
      title: `${singular} forgot password response`
    },
    [componentName('schemas', singular, { suffix: 'ResetPasswordResponse' })]: {
      type: 'object',
      properties: {
        user: composeRef('schemas', singular),
        token: { type: 'string' }
      },
      required: ['user', 'token'],
      title: `${singular} reset password response`
    },
    [componentName('schemas', singular, { suffix: 'VerifyResponse' })]: {
      type: 'object',
      properties: {
        message: { type: 'string' }
      },
      required: ['message'],
      title: `${singular} verify response`
    }
  }
}

const generateComponents = (payload: PayloadRequest['payload']) => {
  const schemas: Record<string, JSONSchema4> = {
    supportedTimezones: {
      type: 'string',
      example: 'Europe/Prague'
    },
    GenericObject: {
      type: 'object',
      title: 'Generic Object',
      description: 'Reference to a filtered collection or external resource',
      properties: {
        id: { type: 'string' }
      },
      additionalProperties: true
    }
  }

  for (const collection of Object.values(payload.collections)) {
    const { singular } = collectionName(collection)
    schemas[componentName('schemas', singular)] = generateSchemaObject(
      payload.config,
      collection
    )
  }

  for (const collection of Object.values(payload.collections)) {
    Object.assign(schemas, generateQueryOperationSchemas(collection))
  }

  // Generate auth schemas only for auth-enabled collections with local strategy enabled
  for (const collection of Object.values(payload.collections)) {
    if (collection.config.auth && !collection.config.auth.disableLocalStrategy) {
      Object.assign(schemas, generateAuthRequestSchemas(collection))
      Object.assign(schemas, generateAuthResponseSchemas(collection))
    }
  }

  // Generate custom endpoint schemas
  for (const collection of Object.values(payload.collections)) {
    if (hasCustomEndpoints(collection)) {
      Object.assign(schemas, generateCustomEndpointRequestSchemas(collection))
      Object.assign(schemas, generateCustomEndpointResponseSchemas(collection))
    }
  }

  for (const global of payload.globals.config.filter(shouldIncludeGlobal)) {
    Object.assign(schemas, generateGlobalSchemas(payload.config, global))
  }

  const generateAuthRequestBodies = (
    collection: Collection
  ): Record<string, OpenAPIV3_1.RequestBodyObject> => {
    const { singular } = collectionName(collection)

    return {
      [componentName('requestBodies', singular, { suffix: 'Login' })]: {
        description: `${singular} login request`,
        content: {
          'application/json': {
            schema: composeRef('schemas', singular, { suffix: 'LoginRequest' })
          }
        }
      },
      [componentName('requestBodies', singular, { suffix: 'ForgotPassword' })]: {
        description: `${singular} forgot password request`,
        content: {
          'application/json': {
            schema: composeRef('schemas', singular, { suffix: 'ForgotPasswordRequest' })
          }
        }
      },
      [componentName('requestBodies', singular, { suffix: 'ResetPassword' })]: {
        description: `${singular} reset password request`,
        content: {
          'application/json': {
            schema: composeRef('schemas', singular, { suffix: 'ResetPasswordRequest' })
          }
        }
      },
      [componentName('requestBodies', singular, { suffix: 'Unlock' })]: {
        description: `${singular} unlock request`,
        content: {
          'application/json': {
            schema: composeRef('schemas', singular, { suffix: 'UnlockRequest' })
          }
        }
      }
    }
  }

  const requestBodies: Record<string, OpenAPIV3_1.RequestBodyObject> = {}

  for (const collection of Object.values(payload.collections)) {
    const { singular } = collectionName(collection)
    requestBodies[componentName('requestBodies', singular)] = generateRequestBodySchema(
      payload.config,
      collection
    )
  }

  // Generate auth request bodies only for auth-enabled collections with local strategy enabled
  for (const collection of Object.values(payload.collections)) {
    if (collection.config.auth && !collection.config.auth.disableLocalStrategy) {
      Object.assign(requestBodies, generateAuthRequestBodies(collection))
    }
  }

  // Generate custom endpoint request bodies
  for (const collection of Object.values(payload.collections)) {
    if (hasCustomEndpoints(collection)) {
      Object.assign(requestBodies, generateCustomEndpointRequestBodies(collection))
    }
  }

  for (const global of payload.globals.config) {
    requestBodies[componentName('requestBodies', globalName(global))] =
      generateGlobalRequestBody(global)
  }

  const generateAuthResponses = (
    collection: Collection
  ): Record<string, OpenAPIV3_1.ResponseObject> => {
    const { singular } = collectionName(collection)

    return {
      [componentName('responses', singular, { suffix: 'Login' })]: {
        description: `${singular} login response`,
        content: {
          'application/json': {
            schema: composeRef('schemas', singular, { suffix: 'LoginResponse' })
          }
        }
      },
      [componentName('responses', singular, { suffix: 'RefreshToken' })]: {
        description: `${singular} refresh token response`,
        content: {
          'application/json': {
            schema: composeRef('schemas', singular, { suffix: 'RefreshTokenResponse' })
          }
        }
      },
      [componentName('responses', singular, { suffix: 'Me' })]: {
        description: `${singular} current user response`,
        content: {
          'application/json': {
            schema: composeRef('schemas', singular, { suffix: 'MeResponse' })
          }
        }
      },
      [componentName('responses', singular, { suffix: 'Logout' })]: {
        description: `${singular} logout response`,
        content: {
          'application/json': {
            schema: composeRef('schemas', singular, { suffix: 'LogoutResponse' })
          }
        }
      },
      [componentName('responses', singular, { suffix: 'ForgotPassword' })]: {
        description: `${singular} forgot password response`,
        content: {
          'application/json': {
            schema: composeRef('schemas', singular, { suffix: 'ForgotPasswordResponse' })
          }
        }
      },
      [componentName('responses', singular, { suffix: 'ResetPassword' })]: {
        description: `${singular} reset password response`,
        content: {
          'application/json': {
            schema: composeRef('schemas', singular, { suffix: 'ResetPasswordResponse' })
          }
        }
      },
      [componentName('responses', singular, { suffix: 'Verify' })]: {
        description: `${singular} verify response`,
        content: {
          'application/json': {
            schema: composeRef('schemas', singular, { suffix: 'VerifyResponse' })
          }
        }
      }
    }
  }

  const responses: Record<string, OpenAPIV3_1.ResponseObject> = Object.assign(
    {},
    ...Object.values(payload.collections).map(generateCollectionResponses),
    // Generate auth responses only for auth-enabled collections with local strategy enabled
    ...Object.values(payload.collections)
      .filter(
        (collection) =>
          collection.config.auth && !collection.config.auth.disableLocalStrategy
      )
      .map(generateAuthResponses),
    // Generate custom endpoint responses
    ...Object.values(payload.collections)
      .filter(hasCustomEndpoints)
      .map(generateCustomEndpointResponses),
    ...payload.globals.config.filter(shouldIncludeGlobal).map((global) => ({
      [componentName('responses', globalName(global))]: generateGlobalResponse(global)
    }))
  )

  return { schemas, requestBodies, responses }
}

const generateAuthOperations = async (
  collection: Collection
): Promise<Record<string, OpenAPIV3.PathItemObject & OpenAPIV3_1.PathItemObject>> => {
  const { slug } = collection.config
  const { singular, plural } = collectionName(collection)
  const tags = [plural]

  return {
    [`/api/${slug}/login`]: {
      post: {
        summary: `Login to ${singular}`,
        tags,
        requestBody: composeRef('requestBodies', singular, { suffix: 'Login' }),
        responses: {
          200: composeRef('responses', singular, { suffix: 'Login' }),
          401: { description: 'Invalid credentials' }
        }
      }
    },
    [`/api/${slug}/logout`]: {
      post: {
        summary: `Logout from ${singular}`,
        tags,
        responses: {
          200: composeRef('responses', singular, { suffix: 'Logout' })
        },
        security: [apiKeySecurity]
      }
    },
    [`/api/${slug}/refresh-token`]: {
      post: {
        summary: `Refresh ${singular} token`,
        tags,
        responses: {
          200: composeRef('responses', singular, { suffix: 'RefreshToken' }),
          401: { description: 'Invalid or expired token' }
        },
        security: [apiKeySecurity]
      }
    },
    [`/api/${slug}/me`]: {
      get: {
        summary: `Get current ${singular}`,
        tags,
        parameters: [...baseQueryParams],
        responses: {
          200: composeRef('responses', singular, { suffix: 'Me' }),
          401: { description: 'Not authenticated' }
        },
        security: [apiKeySecurity]
      }
    },
    [`/api/${slug}/forgot-password`]: {
      post: {
        summary: `Request ${singular} password reset`,
        tags,
        requestBody: composeRef('requestBodies', singular, { suffix: 'ForgotPassword' }),
        responses: {
          200: composeRef('responses', singular, { suffix: 'ForgotPassword' }),
          404: { description: 'User not found' }
        }
      }
    },
    [`/api/${slug}/reset-password`]: {
      post: {
        summary: `Reset ${singular} password`,
        tags,
        requestBody: composeRef('requestBodies', singular, { suffix: 'ResetPassword' }),
        responses: {
          200: composeRef('responses', singular, { suffix: 'ResetPassword' }),
          400: { description: 'Invalid or expired token' }
        }
      }
    },
    [`/api/${slug}/verify/{token}`]: {
      post: {
        summary: `Verify ${singular}`,
        tags,
        parameters: [
          {
            in: 'path',
            name: 'token',
            description: 'Verification token',
            required: true,
            schema: { type: 'string' }
          }
        ],
        responses: {
          200: composeRef('responses', singular, { suffix: 'Verify' }),
          400: { description: 'Invalid or expired token' }
        }
      }
    },
    [`/api/${slug}/unlock`]: {
      post: {
        summary: `Unlock ${singular}`,
        tags,
        requestBody: composeRef('requestBodies', singular, { suffix: 'Unlock' }),
        responses: {
          200: composeRef('responses', singular, { suffix: 'Verify' }),
          400: { description: 'Invalid credentials or account not locked' }
        }
      }
    }
  }
}

const generateGlobalOperations = async (
  global: SanitizedGlobalConfig
): Promise<Record<string, OpenAPIV3.PathItemObject & OpenAPIV3_1.PathItemObject>> => {
  const slug = global.slug
  const singular = globalName(global)
  const tags = [singular]

  return {
    [`/api/globals/${slug}`]: {
      get: {
        summary: `Get the ${singular}`,
        tags,
        parameters: [...baseQueryParams],
        responses: { 200: composeRef('responses', singular) },
        security: (await isOpenToPublic(global.access.read)) ? [] : [apiKeySecurity]
      },
      post: {
        summary: `Update the ${singular}`,
        tags,
        requestBody: composeRef('requestBodies', singular),
        responses: { 200: composeRef('responses', singular) },
        security: (await isOpenToPublic(global.access.update)) ? [] : [apiKeySecurity]
      }
    }
  }
}

export const generateV30Spec = async (
  payload: PayloadRequest['payload'],
  options: SanitizedOpenAPIOptions
): Promise<OpenAPIV3.Document> => {
  // Filter collections to only include specified ones
  const filteredCollections = Object.fromEntries(
    Object.entries(payload.collections).filter(([_, collection]) =>
      shouldIncludeCollection(collection)
    )
  )

  // Create a modified payload object with filtered collections
  const filteredPayload = {
    ...payload,
    collections: filteredCollections
  }

  const { schemas, requestBodies, responses } = generateComponents(
    filteredPayload as PayloadRequest['payload']
  )

  const spec = {
    openapi: '3.0.3',
    info: options.metadata,
    // servers: [
    //   { url: payload.config.serverURL },
    //   { url: process.env.LIVE_SERVER_URL as string }
    // ],
    paths: Object.assign(
      {},
      ...(await Promise.all(
        Object.values(filteredPayload.collections).map(generateCollectionOperations)
      )),
      // Generate auth operations only for auth-enabled collections with local strategy enabled
      ...(await Promise.all(
        Object.values(filteredPayload.collections)
          .filter(
            (collection) =>
              collection.config.auth && !collection.config.auth.disableLocalStrategy
          )
          .map(generateAuthOperations)
      )),
      // Generate custom endpoint operations
      ...(await Promise.all(
        Object.values(filteredPayload.collections)
          .filter(hasCustomEndpoints)
          .map(generateCustomEndpointOperations)
      )),
      ...(await Promise.all(
        payload.globals.config.filter(shouldIncludeGlobal).map(generateGlobalOperations)
      ))
    ),
    components: {
      securitySchemes: generateSecuritySchemes(options.authEndpoint),
      schemas: await mapValuesAsync(jsonSchemaToOpenapiSchema, schemas),
      requestBodies: await mapValuesAsync(
        async (requestBody) => ({
          ...requestBody,
          content: (await mapValuesAsync(
            async (contentItem) => ({
              ...contentItem,
              schema: contentItem.schema
                ? await jsonSchemaToOpenapiSchema(contentItem.schema as JSONSchema4)
                : undefined
            }),
            requestBody.content
          )) as Record<string, OpenAPIV3.MediaTypeObject>
        }),
        requestBodies
      ),
      responses: await mapValuesAsync(async (response) => {
        return {
          ...response,
          content:
            response.content !== undefined
              ? ((await mapValuesAsync(
                  async (contentItem) => ({
                    ...contentItem,
                    schema: contentItem.schema
                      ? await jsonSchemaToOpenapiSchema(contentItem.schema as JSONSchema4)
                      : undefined
                  }),
                  response.content
                )) as Record<string, OpenAPIV3.MediaTypeObject>)
              : {}
        }
      }, responses)
    }
  } satisfies OpenAPIV3.Document

  adjustRefTargets(filteredPayload as PayloadRequest['payload'], spec)

  return spec
}

export const generateV31Spec = async (
  payload: PayloadRequest['payload'],
  options: SanitizedOpenAPIOptions
): Promise<OpenAPIV3_1.Document> => {
  // Filter collections to only include specified ones
  const filteredCollections = Object.fromEntries(
    Object.entries(payload.collections).filter(([_, collection]) =>
      shouldIncludeCollection(collection)
    )
  )

  // Create a modified payload object with filtered collections
  const filteredPayload = {
    ...payload,
    collections: filteredCollections
  }

  const { schemas, requestBodies, responses } = generateComponents(
    filteredPayload as PayloadRequest['payload']
  )

  const spec = {
    openapi: '3.1.0',
    info: options.metadata,
    // servers: [
    //   { url: payload.config.serverURL },
    //   { url: process.env.LIVE_SERVER_URL as string }
    // ],
    paths: Object.assign(
      {},
      ...(await Promise.all(
        Object.values(filteredPayload.collections).map(generateCollectionOperations)
      )),
      // Generate auth operations only for auth-enabled collections with local strategy enabled
      ...(await Promise.all(
        Object.values(filteredPayload.collections)
          .filter(
            (collection) =>
              collection.config.auth && !collection.config.auth.disableLocalStrategy
          )
          .map(generateAuthOperations)
      )),
      // Generate custom endpoint operations
      ...(await Promise.all(
        Object.values(filteredPayload.collections)
          .filter(hasCustomEndpoints)
          .map(generateCustomEndpointOperations)
      )),
      ...(await Promise.all(
        payload.globals.config.filter(shouldIncludeGlobal).map(generateGlobalOperations)
      ))
    ),
    components: {
      securitySchemes: generateSecuritySchemes(options.authEndpoint),
      schemas: schemas as Record<string, OpenAPIV3_1.SchemaObject>,
      requestBodies,
      responses
    }
  } satisfies OpenAPIV3_1.Document

  adjustRefTargets(filteredPayload as PayloadRequest['payload'], spec)

  return spec
}
