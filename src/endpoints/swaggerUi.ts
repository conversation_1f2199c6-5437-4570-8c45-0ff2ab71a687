import type { PayloadRequest } from 'payload'

export const swaggerUIHandler = async (req: PayloadRequest): Promise<Response> => {
  return new Response(
    `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="utf-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <meta
        name="description"
        content="SERP Payload API Documentation"
      />
      <title>SERP Payload API Documentation</title>
      <link href="https://cdn.jsdelivr.net/npm/swagger-ui@5.20.0/dist/swagger-ui.min.css" rel="stylesheet">
    </head>
    <body>
    <div id="swagger-ui"></div>
    <script src="https://cdn.jsdelivr.net/npm/swagger-ui@5.20.0/dist/swagger-ui-bundle.min.js"></script>
    <script>
      window.onload = () => {
        window.ui = SwaggerUIBundle({
          url: '${req.protocol}//${req.headers.get('host')}/api/openapi',
          dom_id: '#swagger-ui',
        });
      };
    </script>
    </body>
    </html>`,
    { headers: { 'content-type': 'text/html' } }
  )
}
