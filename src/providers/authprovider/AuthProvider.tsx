'use client'

import { createContext, useMemo, useState } from 'react'
import { postApiUsersOtpRequestOptions } from '@/lib/api'
import type { User } from '@/lib/api-sdk/types.gen'
import { useMutation } from '@tanstack/react-query'

//import { useMutation, useQuery } from '@tanstack/react-query'

export const AuthContext = createContext<{
  currentUser: User | null
  setCurrentUser: (user: User | null) => void
  // authenticate: (credentials: UserLoginRequest) => void
  // isAuthenticating: boolean
  // authenticateError: Error | null
}>({
  currentUser: null,
  setCurrentUser: () => {}
  // authenticate: () => {},
  // isAuthenticating: false,
  // authenticateError: null
})

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null)
  // const getCurrentUserQuery = useQuery({
  //   ...getApiUsersMeOptions(),
  //   staleTime: 0
  // })

  // const requestOtpMutation = useMutation({
  //   ...postApiUsersOtpRequestOptions(),
  //   onSuccess: (data) => {
  //     if (data?.user) {
  //       setCurrentUser(data.user)
  //     }
  //   }
  // })

  // useEffect(() => {
  //   if (getCurrentUserQuery.data?.user) {
  //     setCurrentUser(getCurrentUserQuery.data.user)
  //   }
  // }, [getCurrentUserQuery.data])

  const value = useMemo(
    () => ({
      currentUser,
      setCurrentUser
      // authenticate: (credentials: UserLoginRequest) =>
      //   authMutation.mutate({
      //     body: credentials
      //   }),
      // isAuthenticating: authMutation.isPending,
      // authenticateError: authMutation.error
    }),
    [currentUser, setCurrentUser]
  )

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
