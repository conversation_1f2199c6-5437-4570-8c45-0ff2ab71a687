'use client'

import { createContext, useMemo, useState } from 'react'
import { postApiUsersOtpRequestMutation,  } from '@/api'
import type { User } from 'payload'
import { useMutation } from '@tanstack/react-query'
import type {  UserOtpRequestRequest} from '@/api'



export const AuthContext = createContext<{
  currentUser: User | null
  setCurrentUser: (user: User | null) => void
  requestOtpMutation: (credentials: UserOtpRequestRequest) => void
  isRequestingOtp: boolean
  requestOtpError: Error | null
}>({
  currentUser: null,
  setCurrentUser: () => {}
  requestOtpMutation: () => {},
  isRequestingOtp: false,
  requestOtpError: null
})

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null)
  // const getCurrentUserQuery = useQuery({
  //   ...getApiUsersMeOptions(),
  //   staleTime: 0
  // })

  const requestOtpMutation = useMutation({
    ...postApiUsersOtpRequestMutation(),
    onSuccess: (data) => {
      console.log(data, 'OTP sent')
    }
  })

  // useEffect(() => {
  //   if (getCurrentUserQuery.data?.user) {
  //     setCurrentUser(getCurrentUserQuery.data.user)
  //   }
  // }, [getCurrentUserQuery.data])

  const value = useMemo(
    () => ({
      currentUser,
      setCurrentUser,
      requestOtpMutation: requestOtpMutation.mutate,
      isRequestingOtp: requestOtpMutation.isPending,
      requestOtpError: requestOtpMutation.error
    }),
    [currentUser, setCurrentUser, requestOtpMutation]
  )

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
