'use client'

import React from 'react'
import {
  AuthProvider,
  ClientProvider,
  HeaderThemeProvider,
  ThemeProvider
} from '@/providers'

export const Providers: React.FC<{
  children: React.ReactNode
}> = ({ children }) => {
  return (
    <ClientProvider>
      <AuthProvider>
        <ThemeProvider>
          <HeaderThemeProvider>{children}</HeaderThemeProvider>
        </ThemeProvider>
      </AuthProvider>
    </ClientProvider>
  )
}
