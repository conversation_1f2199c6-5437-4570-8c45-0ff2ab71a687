import { generateCookie, getCookieExpiration } from 'payload'
import type { SanitizedCollectionConfig } from 'payload'

type GeneratePayloadCookieArgs = {
  collectionAuthConfig: SanitizedCollectionConfig['auth']
  cookiePrefix: string
  returnCookieAsObject?: boolean
  token: string
}

export const generateSerpCookie = ({
  collectionAuthConfig,
  cookiePrefix,
  returnCookieAsObject = false,
  token
}: GeneratePayloadCookieArgs) => {
  const sameSite =
    typeof collectionAuthConfig.cookies.sameSite === 'string'
      ? collectionAuthConfig.cookies.sameSite
      : collectionAuthConfig.cookies.sameSite
        ? 'Strict'
        : undefined
  return generateCookie({
    name: cookiePrefix,
    domain: collectionAuthConfig.cookies.domain ?? undefined,
    expires: getCookieExpiration({
      seconds: collectionAuthConfig.tokenExpiration
    }),
    httpOnly: true,
    path: '/',
    returnCookieAsObject,
    sameSite,
    secure: collectionAuthConfig.cookies.secure,
    value: token
  })
}
