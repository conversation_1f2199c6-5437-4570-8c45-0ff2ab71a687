import fs from 'node:fs'
import { getPayload } from 'payload'
import { generateV30Spec } from '@/openapi/generators'
import type { SanitizedOpenAPIOptions } from '@/openapi/types'
import configPromise from '@/payload.config'

// OpenAPI generation options
const openApiOptions: SanitizedOpenAPIOptions = {
  openapiVersion: '3.0',
  authEndpoint: 'users/login',
  metadata: {
    title: 'SERP Payload API',
    version: '1.0.0',
    description: 'Auto-generated API documentation for SERP Payload CMS'
  }
}

export const generateApi = async (): Promise<string> => {
  try {
    console.log('🚀 Starting OpenAPI generation...')

    // Get payload configuration
    const config = await configPromise

    // Initialize payload instance
    const payload = await getPayload({ config })

    console.log('📊 Generating OpenAPI specification...')

    // Generate OpenAPI specification using our generators
    const spec = await generateV30Spec(payload, openApiOptions)

    // Save to file
    const outputPath = './openapi.json'
    fs.writeFileSync(outputPath, JSON.stringify(spec, null, 2))

    console.log(`✅ OpenAPI specification generated: ${outputPath}`)
    console.log(`📋 Collections included: ${Object.keys(payload.collections).join(', ')}`)
    console.log(
      `🌍 Globals included: ${payload.globals.config.map((g) => g.slug).join(', ')}`
    )

    return outputPath
  } catch (error) {
    console.error('❌ Error generating OpenAPI specification:', error)
    throw error
  }
}
