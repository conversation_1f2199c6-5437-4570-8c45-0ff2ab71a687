import fs from 'node:fs'
import { getPayload } from 'payload'
import { generateV30Spec } from '@/openapi/generators'
import type { SanitizedOpenAPIOptions } from '@/openapi/types'
import configPromise from '@/payload.config'

// OpenAPI generation options
const openApiOptions: SanitizedOpenAPIOptions = {
  openapiVersion: '3.0',
  authEndpoint: 'users/login',
  metadata: {
    title: 'SERP Payload API',
    version: '1.0.0',
    description: 'Auto-generated API documentation for SERP Payload CMS'
  }
}

export const generateApi = async (): Promise<string> => {
  try {
    console.log('🚀 Starting OpenAPI generation...')

    const config = await configPromise

    const payload = await getPayload({ config })

    console.log('📊 Generating OpenAPI specification...')

    const spec = await generateV30Spec(payload, openApiOptions)

    const outputPath = './openapi.json'
    fs.writeFileSync(outputPath, JSON.stringify(spec, null, 2))

    return outputPath
  } catch (error) {
    console.error('❌ Error generating OpenAPI specification:', error)
    throw error
  }
}
