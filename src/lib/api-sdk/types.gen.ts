// This file is auto-generated by @hey-api/openapi-ts

export type SupportedTimezones = string;

/**
 * Admin
 */
export type Admin = {
    id: string;
    name: string;
    role: 'editor' | 'admin';
    updatedAt: string;
    createdAt: string;
    email: string;
    resetPasswordToken?: string | null;
    resetPasswordExpiration?: string | null;
    salt?: string | null;
    hash?: string | null;
    loginAttempts?: number | null;
    lockUntil?: string | null;
    password?: string | null;
};

/**
 * Page
 */
export type Page = {
    id: string;
    title: string;
    hero: {
        type: 'none' | 'highImpact' | 'mediumImpact' | 'lowImpact';
        richText?: {
            root: {
                type: string;
                children: Array<{
                    type: string;
                    version: number;
                    [key: string]: unknown | string | number;
                }>;
                direction: 'ltr' | 'rtl';
                format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
                indent: number;
                version: number;
            };
        } | null;
        links?: Array<{
            link: {
                type?: 'reference' | 'custom';
                newTab?: boolean | null;
                reference?: {
                    relationTo: 'pages';
                    value: string | Page;
                } | null | {
                    relationTo: 'posts';
                    value: string | Post;
                };
                url?: string | null;
                label: string;
                /**
                 * Choose how the link should be rendered.
                 */
                appearance?: 'default' | 'outline';
            };
            id?: string | null;
        }> | null;
        media?: string | null | Media;
    };
    layout: Array<{
        richText?: {
            root: {
                type: string;
                children: Array<{
                    type: string;
                    version: number;
                    [key: string]: unknown | string | number;
                }>;
                direction: 'ltr' | 'rtl';
                format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
                indent: number;
                version: number;
            };
        } | null;
        links?: Array<{
            link: {
                type?: 'reference' | 'custom';
                newTab?: boolean | null;
                reference?: {
                    relationTo: 'pages';
                    value: string | Page;
                } | null | {
                    relationTo: 'posts';
                    value: string | Post;
                };
                url?: string | null;
                label: string;
                /**
                 * Choose how the link should be rendered.
                 */
                appearance?: 'default' | 'outline';
            };
            id?: string | null;
        }> | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'cta';
    } | {
        columns?: Array<{
            size?: 'oneThird' | 'half' | 'twoThirds' | 'full';
            richText?: {
                root: {
                    type: string;
                    children: Array<{
                        type: string;
                        version: number;
                        [key: string]: unknown | string | number;
                    }>;
                    direction: 'ltr' | 'rtl';
                    format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
                    indent: number;
                    version: number;
                };
            } | null;
            enableLink?: boolean | null;
            link?: {
                type?: 'reference' | 'custom';
                newTab?: boolean | null;
                reference?: {
                    relationTo: 'pages';
                    value: string | Page;
                } | null | {
                    relationTo: 'posts';
                    value: string | Post;
                };
                url?: string | null;
                label: string;
                /**
                 * Choose how the link should be rendered.
                 */
                appearance?: 'default' | 'outline';
            };
            id?: string | null;
        }> | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'content';
    } | {
        media: string | Media;
        id?: string | null;
        blockName?: string | null;
        blockType: 'mediaBlock';
    } | {
        introContent?: {
            root: {
                type: string;
                children: Array<{
                    type: string;
                    version: number;
                    [key: string]: unknown | string | number;
                }>;
                direction: 'ltr' | 'rtl';
                format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
                indent: number;
                version: number;
            };
        } | null;
        populateBy?: 'collection' | 'selection';
        relationTo?: 'posts';
        categories?: Array<string | Category> | null;
        limit?: number | null;
        selectedDocs?: Array<string | Post> | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'archive';
    } | {
        form: string | Form;
        enableIntro?: boolean | null;
        introContent?: {
            root: {
                type: string;
                children: Array<{
                    type: string;
                    version: number;
                    [key: string]: unknown | string | number;
                }>;
                direction: 'ltr' | 'rtl';
                format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
                indent: number;
                version: number;
            };
        } | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'formBlock';
    }>;
    meta?: {
        title?: string | null;
        /**
         * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
         */
        image?: string | null | Media;
        description?: string | null;
    };
    publishedAt?: string | null;
    slug?: string | null;
    slugLock?: boolean | null;
    updatedAt: string;
    createdAt: string;
    _status?: 'draft' | 'published';
};

/**
 * Post
 */
export type Post = {
    id: string;
    title: string;
    heroImage?: string | null | Media;
    content: {
        root: {
            type: string;
            children: Array<{
                type: string;
                version: number;
                [key: string]: unknown | string | number;
            }>;
            direction: 'ltr' | 'rtl';
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
        };
    };
    relatedPosts?: Array<string | Post> | null;
    categories?: Array<string | Category> | null;
    meta?: {
        title?: string | null;
        /**
         * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
         */
        image?: string | null | Media;
        description?: string | null;
    };
    publishedAt?: string | null;
    authors?: Array<string | User> | null;
    populatedAuthors?: Array<{
        id?: string | null;
        name?: string | null;
    }> | null;
    slug?: string | null;
    slugLock?: boolean | null;
    updatedAt: string;
    createdAt: string;
    _status?: 'draft' | 'published';
};

/**
 * Media
 */
export type Media = {
    id: string;
    alt?: string | null;
    caption?: {
        root: {
            type: string;
            children: Array<{
                type: string;
                version: number;
                [key: string]: unknown | string | number;
            }>;
            direction: 'ltr' | 'rtl';
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
        };
    } | null;
    prefix?: string | null;
    updatedAt: string;
    createdAt: string;
    url?: string | null;
    thumbnailURL?: string | null;
    filename?: string | null;
    mimeType?: string | null;
    filesize?: number | null;
    width?: number | null;
    height?: number | null;
    focalX?: number | null;
    focalY?: number | null;
    sizes?: {
        thumbnail?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
        square?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
        small?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
        medium?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
        large?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
        xlarge?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
        og?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
    };
};

/**
 * Category
 */
export type Category = {
    id: string;
    title: string;
    slug?: string | null;
    slugLock?: boolean | null;
    parent?: string | null | Category;
    breadcrumbs?: Array<{
        doc?: string | null | Category;
        url?: string | null;
        label?: string | null;
        id?: string | null;
    }> | null;
    updatedAt: string;
    createdAt: string;
};

/**
 * User
 */
export type User = {
    id: string;
    email: string;
    firstName?: string | null;
    lastName?: string | null;
    /**
     * Upload a profile image for the user
     */
    profileImage?: string | null | Media;
    /**
     * Social accounts for the user
     */
    socialAccounts?: Array<{
        provider: string;
        providerId: string;
        isActive?: boolean | null;
        linkedAt?: string | null;
        id?: string | null;
    }> | null;
    teams?: Array<{
        team: string | Team;
        roles: Array<'teamadmin' | 'teamviewer'>;
        id?: string | null;
    }> | null;
    _otp?: string | null;
    _otpExpiration?: string | null;
    updatedAt: string;
    createdAt: string;
};

/**
 * Team
 */
export type Team = {
    id: string;
    name: string;
    image?: string | null | Media;
    user: string | User;
    updatedAt: string;
    createdAt: string;
};

/**
 * Project
 */
export type Project = {
    id: string;
    team?: string | null | Team;
    name: string;
    domain: string;
    projectGoal?: string | null;
    competitorsDomains?: Array<{
        domain: string;
        id?: string | null;
    }> | null;
    image?: string | null | Media;
    user: string | User;
    keywordsCount?: number | null;
    updatedAt: string;
    createdAt: string;
};

/**
 * Keyword
 */
export type Keyword = {
    id: string;
    keyword: string;
    location: string;
    refreshInterval: 'hourly' | 'daily' | 'weekly' | 'monthly';
    device: Array<'desktop' | 'mobile'>;
    tags?: Array<{
        tag?: string | null;
        id?: string | null;
    }> | null;
    analytics?: Array<{
        rankDistrubution?: number | null;
        serpFeatures?: number | null;
        volatility?: number | null;
        id?: string | null;
    }> | null;
    project: string | Project;
    user: string | User;
    updatedAt: string;
    createdAt: string;
};

/**
 * Asset
 */
export type Asset = {
    id: string;
    team?: string | null | Team;
    alt?: string | null;
    caption?: {
        root: {
            type: string;
            children: Array<{
                type: string;
                version: number;
                [key: string]: unknown | string | number;
            }>;
            direction: 'ltr' | 'rtl';
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
        };
    } | null;
    prefix?: string | null;
    updatedAt: string;
    createdAt: string;
    url?: string | null;
    thumbnailURL?: string | null;
    filename?: string | null;
    mimeType?: string | null;
    filesize?: number | null;
    width?: number | null;
    height?: number | null;
    focalX?: number | null;
    focalY?: number | null;
    sizes?: {
        thumbnail?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
    };
};

/**
 * Screenshot
 */
export type Screenshot = {
    id: string;
    team?: string | null | Team;
    name: string;
    user: string | User;
    image: string | Asset;
    updatedAt: string;
    createdAt: string;
};

/**
 * Product
 */
export type Product = {
    id: string;
    name?: string | null;
    description?: string | null;
    stripeID?: string | null;
    skipSync?: boolean | null;
    updatedAt: string;
    createdAt: string;
};

/**
 * Subscription
 */
export type Subscription = {
    id: string;
    team?: string | null | Team;
    subscriptionId: string;
    updatedAt: string;
    createdAt: string;
};

/**
 * Session
 */
export type Session = {
    id: string;
    familyId: string;
    userId: string;
    token: string;
    expiresAt: string;
    ipAddress?: string | null;
    userAgent?: string | null;
    updatedAt: string;
    createdAt: string;
};

/**
 * Redirect
 */
export type Redirect = {
    id: string;
    /**
     * You will need to rebuild the website when changing this field.
     */
    from: string;
    to?: {
        type?: 'reference' | 'custom';
        reference?: {
            relationTo: 'pages';
            value: string | Page;
        } | null | {
            relationTo: 'posts';
            value: string | Post;
        };
        url?: string | null;
    };
    updatedAt: string;
    createdAt: string;
};

/**
 * Form
 */
export type Form = {
    id: string;
    title: string;
    fields?: Array<{
        name: string;
        label?: string | null;
        width?: number | null;
        required?: boolean | null;
        defaultValue?: boolean | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'checkbox';
    } | {
        name: string;
        label?: string | null;
        width?: number | null;
        required?: boolean | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'country';
    } | {
        name: string;
        label?: string | null;
        width?: number | null;
        required?: boolean | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'email';
    } | {
        message?: {
            root: {
                type: string;
                children: Array<{
                    type: string;
                    version: number;
                    [key: string]: unknown | string | number;
                }>;
                direction: 'ltr' | 'rtl';
                format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
                indent: number;
                version: number;
            };
        } | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'message';
    } | {
        name: string;
        label?: string | null;
        width?: number | null;
        defaultValue?: number | null;
        required?: boolean | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'number';
    } | {
        name: string;
        label?: string | null;
        width?: number | null;
        defaultValue?: string | null;
        placeholder?: string | null;
        options?: Array<{
            label: string;
            value: string;
            id?: string | null;
        }> | null;
        required?: boolean | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'select';
    } | {
        name: string;
        label?: string | null;
        width?: number | null;
        required?: boolean | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'state';
    } | {
        name: string;
        label?: string | null;
        width?: number | null;
        defaultValue?: string | null;
        required?: boolean | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'text';
    } | {
        name: string;
        label?: string | null;
        width?: number | null;
        defaultValue?: string | null;
        required?: boolean | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'textarea';
    }> | null;
    submitButtonLabel?: string | null;
    /**
     * Choose whether to display an on-page message or redirect to a different page after they submit the form.
     */
    confirmationType?: 'message' | 'redirect';
    confirmationMessage?: {
        root: {
            type: string;
            children: Array<{
                type: string;
                version: number;
                [key: string]: unknown | string | number;
            }>;
            direction: 'ltr' | 'rtl';
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
        };
    } | null;
    redirect?: {
        url: string;
    };
    /**
     * Send custom emails when the form submits. Use comma separated lists to send the same email to multiple recipients. To reference a value from this form, wrap that field's name with double curly brackets, i.e. {{firstName}}. You can use a wildcard {{*}} to output all data and {{*:table}} to format it as an HTML table in the email.
     */
    emails?: Array<{
        emailTo?: string | null;
        cc?: string | null;
        bcc?: string | null;
        replyTo?: string | null;
        emailFrom?: string | null;
        subject: string;
        /**
         * Enter the message that should be sent in this email.
         */
        message?: {
            root: {
                type: string;
                children: Array<{
                    type: string;
                    version: number;
                    [key: string]: unknown | string | number;
                }>;
                direction: 'ltr' | 'rtl';
                format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
                indent: number;
                version: number;
            };
        } | null;
        id?: string | null;
    }> | null;
    updatedAt: string;
    createdAt: string;
};

/**
 * Form Submission
 */
export type FormSubmission = {
    id: string;
    form: string | Form;
    submissionData?: Array<{
        field: string;
        value: string;
        id?: string | null;
    }> | null;
    updatedAt: string;
    createdAt: string;
};

/**
 * Search Result
 * This is a collection of automatically created search results. These results are used by the global site search and will be updated automatically as documents in the CMS are created or updated.
 */
export type SearchResult = {
    id: string;
    title?: string | null;
    priority?: number | null;
    doc: {
        relationTo: 'posts';
        value: string | Post;
    };
    slug?: string | null;
    meta?: {
        title?: string | null;
        description?: string | null;
        image?: string | null | Media;
    };
    categories?: Array<{
        relationTo?: string | null;
        categoryID?: string | null;
        title?: string | null;
        id?: string | null;
    }> | null;
    updatedAt: string;
    createdAt: string;
};

/**
 * Payload Job
 */
export type PayloadJob = {
    id: string;
    /**
     * Input data provided to the job
     */
    input?: {
        [key: string]: unknown;
    } | Array<unknown> | string | number | boolean | null;
    taskStatus?: {
        [key: string]: unknown;
    } | Array<unknown> | string | number | boolean | null;
    completedAt?: string | null;
    totalTried?: number | null;
    /**
     * If hasError is true this job will not be retried
     */
    hasError?: boolean | null;
    /**
     * If hasError is true, this is the error that caused it
     */
    error?: {
        [key: string]: unknown;
    } | Array<unknown> | string | number | boolean | null;
    /**
     * Task execution log
     */
    log?: Array<{
        executedAt: string;
        completedAt: string;
        taskSlug: 'inline' | 'schedulePublish';
        taskID: string;
        input?: {
            [key: string]: unknown;
        } | Array<unknown> | string | number | boolean | null;
        output?: {
            [key: string]: unknown;
        } | Array<unknown> | string | number | boolean | null;
        state: 'failed' | 'succeeded';
        error?: {
            [key: string]: unknown;
        } | Array<unknown> | string | number | boolean | null;
        id?: string | null;
    }> | null;
    taskSlug?: 'inline' | 'schedulePublish';
    queue?: string | null;
    waitUntil?: string | null;
    processing?: boolean | null;
    updatedAt: string;
    createdAt: string;
};

/**
 * Payload Locked Document
 */
export type PayloadLockedDocument = {
    id: string;
    document?: {
        relationTo: 'admins';
        value: string | Admin;
    } | null | {
        relationTo: 'pages';
        value: string | Page;
    } | {
        relationTo: 'posts';
        value: string | Post;
    } | {
        relationTo: 'media';
        value: string | Media;
    } | {
        relationTo: 'categories';
        value: string | Category;
    } | {
        relationTo: 'users';
        value: string | User;
    } | {
        relationTo: 'teams';
        value: string | Team;
    } | {
        relationTo: 'projects';
        value: string | Project;
    } | {
        relationTo: 'keywords';
        value: string | Keyword;
    } | {
        relationTo: 'assets';
        value: string | Asset;
    } | {
        relationTo: 'screenshots';
        value: string | Screenshot;
    } | {
        relationTo: 'products';
        value: string | Product;
    } | {
        relationTo: 'subscriptions';
        value: string | Subscription;
    } | {
        relationTo: 'sessions';
        value: string | Session;
    } | {
        relationTo: 'redirects';
        value: string | Redirect;
    } | {
        relationTo: 'forms';
        value: string | Form;
    } | {
        relationTo: 'form-submissions';
        value: string | FormSubmission;
    } | {
        relationTo: 'search';
        value: string | SearchResult;
    } | {
        relationTo: 'payload-jobs';
        value: string | PayloadJob;
    };
    globalSlug?: string | null;
    user: {
        relationTo: 'admins';
        value: string | Admin;
    } | {
        relationTo: 'users';
        value: string | User;
    };
    updatedAt: string;
    createdAt: string;
};

/**
 * Payload Preference
 */
export type PayloadPreference = {
    id: string;
    user: {
        relationTo: 'admins';
        value: string | Admin;
    } | {
        relationTo: 'users';
        value: string | User;
    };
    key?: string | null;
    value?: {
        [key: string]: unknown;
    } | Array<unknown> | string | number | boolean | null;
    updatedAt: string;
    createdAt: string;
};

/**
 * Payload Migration
 */
export type PayloadMigration = {
    id: string;
    name?: string | null;
    batch?: number | null;
    updatedAt: string;
    createdAt: string;
};

/**
 * Admin query operations
 */
export type AdminQueryOperations = {
    id?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    name?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    role?: {
        equals?: 'editor' | 'admin';
        not_equals?: 'editor' | 'admin';
        in?: string;
        not_in?: string;
    };
    updatedAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    createdAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    email?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        contains?: string;
    };
    resetPasswordToken?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    resetPasswordExpiration?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    salt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    hash?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    loginAttempts?: {
        equals?: number;
        not_equals?: number;
        in?: string;
        not_in?: string;
        greater_than?: number;
        greater_than_equal?: number;
        less_than?: number;
        less_than_equal?: number;
    };
    lockUntil?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
};

/**
 * Admin query conjunction
 */
export type AdminQueryOperationsAnd = {
    and: Array<AdminQueryOperations | AdminQueryOperationsAnd | AdminQueryOperationsOr>;
};

/**
 * Admin query disjunction
 */
export type AdminQueryOperationsOr = {
    or: Array<AdminQueryOperations | AdminQueryOperationsAnd | AdminQueryOperationsOr>;
};

/**
 * Page query operations
 */
export type PageQueryOperations = {
    title?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    publishedAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    slug?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    slugLock?: {
        equals?: boolean;
        not_equals?: boolean;
        in?: string;
        not_in?: string;
    };
    updatedAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    createdAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    _status?: {
        equals?: 'draft' | 'published';
        not_equals?: 'draft' | 'published';
        in?: string;
        not_in?: string;
    };
};

/**
 * Page query conjunction
 */
export type PageQueryOperationsAnd = {
    and: Array<PageQueryOperations | PageQueryOperationsAnd | PageQueryOperationsOr>;
};

/**
 * Page query disjunction
 */
export type PageQueryOperationsOr = {
    or: Array<PageQueryOperations | PageQueryOperationsAnd | PageQueryOperationsOr>;
};

/**
 * Post query operations
 */
export type PostQueryOperations = {
    title?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    publishedAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    slug?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    slugLock?: {
        equals?: boolean;
        not_equals?: boolean;
        in?: string;
        not_in?: string;
    };
    updatedAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    createdAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    _status?: {
        equals?: 'draft' | 'published';
        not_equals?: 'draft' | 'published';
        in?: string;
        not_in?: string;
    };
};

/**
 * Post query conjunction
 */
export type PostQueryOperationsAnd = {
    and: Array<PostQueryOperations | PostQueryOperationsAnd | PostQueryOperationsOr>;
};

/**
 * Post query disjunction
 */
export type PostQueryOperationsOr = {
    or: Array<PostQueryOperations | PostQueryOperationsAnd | PostQueryOperationsOr>;
};

/**
 * Media query operations
 */
export type MediaQueryOperations = {
    alt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    prefix?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    updatedAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    createdAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    url?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    thumbnailURL?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    filename?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    mimeType?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    filesize?: {
        equals?: number;
        not_equals?: number;
        in?: string;
        not_in?: string;
        greater_than?: number;
        greater_than_equal?: number;
        less_than?: number;
        less_than_equal?: number;
    };
    width?: {
        equals?: number;
        not_equals?: number;
        in?: string;
        not_in?: string;
        greater_than?: number;
        greater_than_equal?: number;
        less_than?: number;
        less_than_equal?: number;
    };
    height?: {
        equals?: number;
        not_equals?: number;
        in?: string;
        not_in?: string;
        greater_than?: number;
        greater_than_equal?: number;
        less_than?: number;
        less_than_equal?: number;
    };
    focalX?: {
        equals?: number;
        not_equals?: number;
        in?: string;
        not_in?: string;
        greater_than?: number;
        greater_than_equal?: number;
        less_than?: number;
        less_than_equal?: number;
    };
    focalY?: {
        equals?: number;
        not_equals?: number;
        in?: string;
        not_in?: string;
        greater_than?: number;
        greater_than_equal?: number;
        less_than?: number;
        less_than_equal?: number;
    };
};

/**
 * Media query conjunction
 */
export type MediaQueryOperationsAnd = {
    and: Array<MediaQueryOperations | MediaQueryOperationsAnd | MediaQueryOperationsOr>;
};

/**
 * Media query disjunction
 */
export type MediaQueryOperationsOr = {
    or: Array<MediaQueryOperations | MediaQueryOperationsAnd | MediaQueryOperationsOr>;
};

/**
 * Category query operations
 */
export type CategoryQueryOperations = {
    title?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    slug?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    slugLock?: {
        equals?: boolean;
        not_equals?: boolean;
        in?: string;
        not_in?: string;
    };
    updatedAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    createdAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
};

/**
 * Category query conjunction
 */
export type CategoryQueryOperationsAnd = {
    and: Array<CategoryQueryOperations | CategoryQueryOperationsAnd | CategoryQueryOperationsOr>;
};

/**
 * Category query disjunction
 */
export type CategoryQueryOperationsOr = {
    or: Array<CategoryQueryOperations | CategoryQueryOperationsAnd | CategoryQueryOperationsOr>;
};

/**
 * User query operations
 */
export type UserQueryOperations = {
    id?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    email?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        contains?: string;
    };
    firstName?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    lastName?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    _otp?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    _otpExpiration?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    updatedAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    createdAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
};

/**
 * User query conjunction
 */
export type UserQueryOperationsAnd = {
    and: Array<UserQueryOperations | UserQueryOperationsAnd | UserQueryOperationsOr>;
};

/**
 * User query disjunction
 */
export type UserQueryOperationsOr = {
    or: Array<UserQueryOperations | UserQueryOperationsAnd | UserQueryOperationsOr>;
};

/**
 * Team query operations
 */
export type TeamQueryOperations = {
    name?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    updatedAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    createdAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
};

/**
 * Team query conjunction
 */
export type TeamQueryOperationsAnd = {
    and: Array<TeamQueryOperations | TeamQueryOperationsAnd | TeamQueryOperationsOr>;
};

/**
 * Team query disjunction
 */
export type TeamQueryOperationsOr = {
    or: Array<TeamQueryOperations | TeamQueryOperationsAnd | TeamQueryOperationsOr>;
};

/**
 * Project query operations
 */
export type ProjectQueryOperations = {
    name?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    domain?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    keywordsCount?: {
        equals?: number;
        not_equals?: number;
        in?: string;
        not_in?: string;
        greater_than?: number;
        greater_than_equal?: number;
        less_than?: number;
        less_than_equal?: number;
    };
    updatedAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    createdAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
};

/**
 * Project query conjunction
 */
export type ProjectQueryOperationsAnd = {
    and: Array<ProjectQueryOperations | ProjectQueryOperationsAnd | ProjectQueryOperationsOr>;
};

/**
 * Project query disjunction
 */
export type ProjectQueryOperationsOr = {
    or: Array<ProjectQueryOperations | ProjectQueryOperationsAnd | ProjectQueryOperationsOr>;
};

/**
 * Keyword query operations
 */
export type KeywordQueryOperations = {
    keyword?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    location?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    refreshInterval?: {
        equals?: 'hourly' | 'daily' | 'weekly' | 'monthly';
        not_equals?: 'hourly' | 'daily' | 'weekly' | 'monthly';
        in?: string;
        not_in?: string;
    };
    device?: {
        equals?: 'desktop' | 'mobile';
        not_equals?: 'desktop' | 'mobile';
        in?: string;
        not_in?: string;
    };
    updatedAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    createdAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
};

/**
 * Keyword query conjunction
 */
export type KeywordQueryOperationsAnd = {
    and: Array<KeywordQueryOperations | KeywordQueryOperationsAnd | KeywordQueryOperationsOr>;
};

/**
 * Keyword query disjunction
 */
export type KeywordQueryOperationsOr = {
    or: Array<KeywordQueryOperations | KeywordQueryOperationsAnd | KeywordQueryOperationsOr>;
};

/**
 * Asset query operations
 */
export type AssetQueryOperations = {
    alt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    prefix?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    updatedAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    createdAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    url?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    thumbnailURL?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    filename?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    mimeType?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    filesize?: {
        equals?: number;
        not_equals?: number;
        in?: string;
        not_in?: string;
        greater_than?: number;
        greater_than_equal?: number;
        less_than?: number;
        less_than_equal?: number;
    };
    width?: {
        equals?: number;
        not_equals?: number;
        in?: string;
        not_in?: string;
        greater_than?: number;
        greater_than_equal?: number;
        less_than?: number;
        less_than_equal?: number;
    };
    height?: {
        equals?: number;
        not_equals?: number;
        in?: string;
        not_in?: string;
        greater_than?: number;
        greater_than_equal?: number;
        less_than?: number;
        less_than_equal?: number;
    };
    focalX?: {
        equals?: number;
        not_equals?: number;
        in?: string;
        not_in?: string;
        greater_than?: number;
        greater_than_equal?: number;
        less_than?: number;
        less_than_equal?: number;
    };
    focalY?: {
        equals?: number;
        not_equals?: number;
        in?: string;
        not_in?: string;
        greater_than?: number;
        greater_than_equal?: number;
        less_than?: number;
        less_than_equal?: number;
    };
};

/**
 * Asset query conjunction
 */
export type AssetQueryOperationsAnd = {
    and: Array<AssetQueryOperations | AssetQueryOperationsAnd | AssetQueryOperationsOr>;
};

/**
 * Asset query disjunction
 */
export type AssetQueryOperationsOr = {
    or: Array<AssetQueryOperations | AssetQueryOperationsAnd | AssetQueryOperationsOr>;
};

/**
 * Screenshot query operations
 */
export type ScreenshotQueryOperations = {
    name?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    updatedAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    createdAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
};

/**
 * Screenshot query conjunction
 */
export type ScreenshotQueryOperationsAnd = {
    and: Array<ScreenshotQueryOperations | ScreenshotQueryOperationsAnd | ScreenshotQueryOperationsOr>;
};

/**
 * Screenshot query disjunction
 */
export type ScreenshotQueryOperationsOr = {
    or: Array<ScreenshotQueryOperations | ScreenshotQueryOperationsAnd | ScreenshotQueryOperationsOr>;
};

/**
 * Product query operations
 */
export type ProductQueryOperations = {
    name?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    description?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    stripeID?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    skipSync?: {
        equals?: boolean;
        not_equals?: boolean;
        in?: string;
        not_in?: string;
    };
    updatedAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    createdAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
};

/**
 * Product query conjunction
 */
export type ProductQueryOperationsAnd = {
    and: Array<ProductQueryOperations | ProductQueryOperationsAnd | ProductQueryOperationsOr>;
};

/**
 * Product query disjunction
 */
export type ProductQueryOperationsOr = {
    or: Array<ProductQueryOperations | ProductQueryOperationsAnd | ProductQueryOperationsOr>;
};

/**
 * Subscription query operations
 */
export type SubscriptionQueryOperations = {
    subscriptionId?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    updatedAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    createdAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
};

/**
 * Subscription query conjunction
 */
export type SubscriptionQueryOperationsAnd = {
    and: Array<SubscriptionQueryOperations | SubscriptionQueryOperationsAnd | SubscriptionQueryOperationsOr>;
};

/**
 * Subscription query disjunction
 */
export type SubscriptionQueryOperationsOr = {
    or: Array<SubscriptionQueryOperations | SubscriptionQueryOperationsAnd | SubscriptionQueryOperationsOr>;
};

/**
 * Session query operations
 */
export type SessionQueryOperations = {
    id?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    familyId?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    userId?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    token?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    expiresAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    ipAddress?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    userAgent?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    updatedAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    createdAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
};

/**
 * Session query conjunction
 */
export type SessionQueryOperationsAnd = {
    and: Array<SessionQueryOperations | SessionQueryOperationsAnd | SessionQueryOperationsOr>;
};

/**
 * Session query disjunction
 */
export type SessionQueryOperationsOr = {
    or: Array<SessionQueryOperations | SessionQueryOperationsAnd | SessionQueryOperationsOr>;
};

/**
 * Redirect query operations
 */
export type RedirectQueryOperations = {
    from?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    updatedAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    createdAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
};

/**
 * Redirect query conjunction
 */
export type RedirectQueryOperationsAnd = {
    and: Array<RedirectQueryOperations | RedirectQueryOperationsAnd | RedirectQueryOperationsOr>;
};

/**
 * Redirect query disjunction
 */
export type RedirectQueryOperationsOr = {
    or: Array<RedirectQueryOperations | RedirectQueryOperationsAnd | RedirectQueryOperationsOr>;
};

/**
 * Form query operations
 */
export type FormQueryOperations = {
    title?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    submitButtonLabel?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    confirmationType?: {
        equals?: 'message' | 'redirect';
        not_equals?: 'message' | 'redirect';
        in?: string;
        not_in?: string;
    };
    updatedAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    createdAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
};

/**
 * Form query conjunction
 */
export type FormQueryOperationsAnd = {
    and: Array<FormQueryOperations | FormQueryOperationsAnd | FormQueryOperationsOr>;
};

/**
 * Form query disjunction
 */
export type FormQueryOperationsOr = {
    or: Array<FormQueryOperations | FormQueryOperationsAnd | FormQueryOperationsOr>;
};

/**
 * Form Submission query operations
 */
export type FormSubmissionQueryOperations = {
    updatedAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    createdAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
};

/**
 * Form Submission query conjunction
 */
export type FormSubmissionQueryOperationsAnd = {
    and: Array<FormSubmissionQueryOperations | FormSubmissionQueryOperationsAnd | FormSubmissionQueryOperationsOr>;
};

/**
 * Form Submission query disjunction
 */
export type FormSubmissionQueryOperationsOr = {
    or: Array<FormSubmissionQueryOperations | FormSubmissionQueryOperationsAnd | FormSubmissionQueryOperationsOr>;
};

/**
 * Search Result query operations
 */
export type SearchResultQueryOperations = {
    title?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    priority?: {
        equals?: number;
        not_equals?: number;
        in?: string;
        not_in?: string;
        greater_than?: number;
        greater_than_equal?: number;
        less_than?: number;
        less_than_equal?: number;
    };
    slug?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    updatedAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    createdAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
};

/**
 * Search Result query conjunction
 */
export type SearchResultQueryOperationsAnd = {
    and: Array<SearchResultQueryOperations | SearchResultQueryOperationsAnd | SearchResultQueryOperationsOr>;
};

/**
 * Search Result query disjunction
 */
export type SearchResultQueryOperationsOr = {
    or: Array<SearchResultQueryOperations | SearchResultQueryOperationsAnd | SearchResultQueryOperationsOr>;
};

/**
 * Payload Job query operations
 */
export type PayloadJobQueryOperations = {
    taskSlug?: {
        equals?: 'inline' | 'schedulePublish';
        not_equals?: 'inline' | 'schedulePublish';
        in?: string;
        not_in?: string;
    };
    queue?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    waitUntil?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    processing?: {
        equals?: boolean;
        not_equals?: boolean;
        in?: string;
        not_in?: string;
    };
    updatedAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    createdAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
};

/**
 * Payload Job query conjunction
 */
export type PayloadJobQueryOperationsAnd = {
    and: Array<PayloadJobQueryOperations | PayloadJobQueryOperationsAnd | PayloadJobQueryOperationsOr>;
};

/**
 * Payload Job query disjunction
 */
export type PayloadJobQueryOperationsOr = {
    or: Array<PayloadJobQueryOperations | PayloadJobQueryOperationsAnd | PayloadJobQueryOperationsOr>;
};

/**
 * Payload Locked Document query operations
 */
export type PayloadLockedDocumentQueryOperations = {
    globalSlug?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    updatedAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    createdAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
};

/**
 * Payload Locked Document query conjunction
 */
export type PayloadLockedDocumentQueryOperationsAnd = {
    and: Array<PayloadLockedDocumentQueryOperations | PayloadLockedDocumentQueryOperationsAnd | PayloadLockedDocumentQueryOperationsOr>;
};

/**
 * Payload Locked Document query disjunction
 */
export type PayloadLockedDocumentQueryOperationsOr = {
    or: Array<PayloadLockedDocumentQueryOperations | PayloadLockedDocumentQueryOperationsAnd | PayloadLockedDocumentQueryOperationsOr>;
};

/**
 * Payload Preference query operations
 */
export type PayloadPreferenceQueryOperations = {
    key?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    updatedAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    createdAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
};

/**
 * Payload Preference query conjunction
 */
export type PayloadPreferenceQueryOperationsAnd = {
    and: Array<PayloadPreferenceQueryOperations | PayloadPreferenceQueryOperationsAnd | PayloadPreferenceQueryOperationsOr>;
};

/**
 * Payload Preference query disjunction
 */
export type PayloadPreferenceQueryOperationsOr = {
    or: Array<PayloadPreferenceQueryOperations | PayloadPreferenceQueryOperationsAnd | PayloadPreferenceQueryOperationsOr>;
};

/**
 * Payload Migration query operations
 */
export type PayloadMigrationQueryOperations = {
    name?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    batch?: {
        equals?: number;
        not_equals?: number;
        in?: string;
        not_in?: string;
        greater_than?: number;
        greater_than_equal?: number;
        less_than?: number;
        less_than_equal?: number;
    };
    updatedAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    createdAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
};

/**
 * Payload Migration query conjunction
 */
export type PayloadMigrationQueryOperationsAnd = {
    and: Array<PayloadMigrationQueryOperations | PayloadMigrationQueryOperationsAnd | PayloadMigrationQueryOperationsOr>;
};

/**
 * Payload Migration query disjunction
 */
export type PayloadMigrationQueryOperationsOr = {
    or: Array<PayloadMigrationQueryOperations | PayloadMigrationQueryOperationsAnd | PayloadMigrationQueryOperationsOr>;
};

/**
 * Admin login request
 */
export type AdminLoginRequest = {
    email: string;
    password: string;
};

/**
 * Admin forgot password request
 */
export type AdminForgotPasswordRequest = {
    email: string;
};

/**
 * Admin reset password request
 */
export type AdminResetPasswordRequest = {
    token: string;
    password: string;
};

/**
 * Admin unlock request
 */
export type AdminUnlockRequest = {
    email: string;
};

/**
 * Admin login response
 */
export type AdminLoginResponse = {
    user: Admin;
    token: string;
    exp: number;
};

/**
 * User refresh token response
 */
export type AdminRefreshTokenResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * Admin me response
 */
export type AdminMeResponse = {
    user: Admin;
    collection: string;
    strategy: string;
    exp: number;
    token: string;
    message?: string;
};

/**
 * Admin logout response
 */
export type AdminLogoutResponse = {
    message: string;
};

/**
 * Admin forgot password response
 */
export type AdminForgotPasswordResponse = {
    message: string;
};

/**
 * Admin reset password response
 */
export type AdminResetPasswordResponse = {
    user: Admin;
    token: string;
};

/**
 * Admin verify response
 */
export type AdminVerifyResponse = {
    message: string;
};

/**
 * User OTP request
 */
export type AdminOtpRequestRequest = {
    email: string;
};

/**
 * User OTP login request
 */
export type AdminOtpLoginRequest = {
    email: string;
    otp: string;
};

/**
 * User OTP request response
 */
export type AdminOtpRequestResponse = {
    type: string;
    message: string;
    value: string;
};

/**
 * User OTP login response
 */
export type AdminOtpLoginResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User OTP request
 */
export type PageOtpRequestRequest = {
    email: string;
};

/**
 * User OTP login request
 */
export type PageOtpLoginRequest = {
    email: string;
    otp: string;
};

/**
 * User OTP request response
 */
export type PageOtpRequestResponse = {
    type: string;
    message: string;
    value: string;
};

/**
 * User OTP login response
 */
export type PageOtpLoginResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User refresh token response
 */
export type PageRefreshTokenResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User OTP request
 */
export type PostOtpRequestRequest = {
    email: string;
};

/**
 * User OTP login request
 */
export type PostOtpLoginRequest = {
    email: string;
    otp: string;
};

/**
 * User OTP request response
 */
export type PostOtpRequestResponse = {
    type: string;
    message: string;
    value: string;
};

/**
 * User OTP login response
 */
export type PostOtpLoginResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User refresh token response
 */
export type PostRefreshTokenResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User OTP request
 */
export type MediaOtpRequestRequest = {
    email: string;
};

/**
 * User OTP login request
 */
export type MediaOtpLoginRequest = {
    email: string;
    otp: string;
};

/**
 * User OTP request response
 */
export type MediaOtpRequestResponse = {
    type: string;
    message: string;
    value: string;
};

/**
 * User OTP login response
 */
export type MediaOtpLoginResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User refresh token response
 */
export type MediaRefreshTokenResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User OTP request
 */
export type CategoryOtpRequestRequest = {
    email: string;
};

/**
 * User OTP login request
 */
export type CategoryOtpLoginRequest = {
    email: string;
    otp: string;
};

/**
 * User OTP request response
 */
export type CategoryOtpRequestResponse = {
    type: string;
    message: string;
    value: string;
};

/**
 * User OTP login response
 */
export type CategoryOtpLoginResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User refresh token response
 */
export type CategoryRefreshTokenResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User OTP request
 */
export type UserOtpRequestRequest = {
    email: string;
};

/**
 * User OTP login request
 */
export type UserOtpLoginRequest = {
    email: string;
    otp: string;
};

/**
 * User OTP request response
 */
export type UserOtpRequestResponse = {
    type: string;
    message: string;
    value: string;
};

/**
 * User OTP login response
 */
export type UserOtpLoginResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User refresh token response
 */
export type UserRefreshTokenResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User OTP request
 */
export type TeamOtpRequestRequest = {
    email: string;
};

/**
 * User OTP login request
 */
export type TeamOtpLoginRequest = {
    email: string;
    otp: string;
};

/**
 * User OTP request response
 */
export type TeamOtpRequestResponse = {
    type: string;
    message: string;
    value: string;
};

/**
 * User OTP login response
 */
export type TeamOtpLoginResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User refresh token response
 */
export type TeamRefreshTokenResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User OTP request
 */
export type ProjectOtpRequestRequest = {
    email: string;
};

/**
 * User OTP login request
 */
export type ProjectOtpLoginRequest = {
    email: string;
    otp: string;
};

/**
 * User OTP request response
 */
export type ProjectOtpRequestResponse = {
    type: string;
    message: string;
    value: string;
};

/**
 * User OTP login response
 */
export type ProjectOtpLoginResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User refresh token response
 */
export type ProjectRefreshTokenResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User OTP request
 */
export type KeywordOtpRequestRequest = {
    email: string;
};

/**
 * User OTP login request
 */
export type KeywordOtpLoginRequest = {
    email: string;
    otp: string;
};

/**
 * User OTP request response
 */
export type KeywordOtpRequestResponse = {
    type: string;
    message: string;
    value: string;
};

/**
 * User OTP login response
 */
export type KeywordOtpLoginResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User refresh token response
 */
export type KeywordRefreshTokenResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User OTP request
 */
export type AssetOtpRequestRequest = {
    email: string;
};

/**
 * User OTP login request
 */
export type AssetOtpLoginRequest = {
    email: string;
    otp: string;
};

/**
 * User OTP request response
 */
export type AssetOtpRequestResponse = {
    type: string;
    message: string;
    value: string;
};

/**
 * User OTP login response
 */
export type AssetOtpLoginResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User refresh token response
 */
export type AssetRefreshTokenResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User OTP request
 */
export type ScreenshotOtpRequestRequest = {
    email: string;
};

/**
 * User OTP login request
 */
export type ScreenshotOtpLoginRequest = {
    email: string;
    otp: string;
};

/**
 * User OTP request response
 */
export type ScreenshotOtpRequestResponse = {
    type: string;
    message: string;
    value: string;
};

/**
 * User OTP login response
 */
export type ScreenshotOtpLoginResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User refresh token response
 */
export type ScreenshotRefreshTokenResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User OTP request
 */
export type ProductOtpRequestRequest = {
    email: string;
};

/**
 * User OTP login request
 */
export type ProductOtpLoginRequest = {
    email: string;
    otp: string;
};

/**
 * User OTP request response
 */
export type ProductOtpRequestResponse = {
    type: string;
    message: string;
    value: string;
};

/**
 * User OTP login response
 */
export type ProductOtpLoginResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User refresh token response
 */
export type ProductRefreshTokenResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User OTP request
 */
export type SubscriptionOtpRequestRequest = {
    email: string;
};

/**
 * User OTP login request
 */
export type SubscriptionOtpLoginRequest = {
    email: string;
    otp: string;
};

/**
 * User OTP request response
 */
export type SubscriptionOtpRequestResponse = {
    type: string;
    message: string;
    value: string;
};

/**
 * User OTP login response
 */
export type SubscriptionOtpLoginResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User refresh token response
 */
export type SubscriptionRefreshTokenResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User OTP request
 */
export type SessionOtpRequestRequest = {
    email: string;
};

/**
 * User OTP login request
 */
export type SessionOtpLoginRequest = {
    email: string;
    otp: string;
};

/**
 * User OTP request response
 */
export type SessionOtpRequestResponse = {
    type: string;
    message: string;
    value: string;
};

/**
 * User OTP login response
 */
export type SessionOtpLoginResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User refresh token response
 */
export type SessionRefreshTokenResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User OTP request
 */
export type RedirectOtpRequestRequest = {
    email: string;
};

/**
 * User OTP login request
 */
export type RedirectOtpLoginRequest = {
    email: string;
    otp: string;
};

/**
 * User OTP request response
 */
export type RedirectOtpRequestResponse = {
    type: string;
    message: string;
    value: string;
};

/**
 * User OTP login response
 */
export type RedirectOtpLoginResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User refresh token response
 */
export type RedirectRefreshTokenResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User OTP request
 */
export type FormOtpRequestRequest = {
    email: string;
};

/**
 * User OTP login request
 */
export type FormOtpLoginRequest = {
    email: string;
    otp: string;
};

/**
 * User OTP request response
 */
export type FormOtpRequestResponse = {
    type: string;
    message: string;
    value: string;
};

/**
 * User OTP login response
 */
export type FormOtpLoginResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User refresh token response
 */
export type FormRefreshTokenResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User OTP request
 */
export type FormSubmissionOtpRequestRequest = {
    email: string;
};

/**
 * User OTP login request
 */
export type FormSubmissionOtpLoginRequest = {
    email: string;
    otp: string;
};

/**
 * User OTP request response
 */
export type FormSubmissionOtpRequestResponse = {
    type: string;
    message: string;
    value: string;
};

/**
 * User OTP login response
 */
export type FormSubmissionOtpLoginResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User refresh token response
 */
export type FormSubmissionRefreshTokenResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User OTP request
 */
export type SearchResultOtpRequestRequest = {
    email: string;
};

/**
 * User OTP login request
 */
export type SearchResultOtpLoginRequest = {
    email: string;
    otp: string;
};

/**
 * User OTP request response
 */
export type SearchResultOtpRequestResponse = {
    type: string;
    message: string;
    value: string;
};

/**
 * User OTP login response
 */
export type SearchResultOtpLoginResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User refresh token response
 */
export type SearchResultRefreshTokenResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User OTP request
 */
export type PayloadJobOtpRequestRequest = {
    email: string;
};

/**
 * User OTP login request
 */
export type PayloadJobOtpLoginRequest = {
    email: string;
    otp: string;
};

/**
 * User OTP request response
 */
export type PayloadJobOtpRequestResponse = {
    type: string;
    message: string;
    value: string;
};

/**
 * User OTP login response
 */
export type PayloadJobOtpLoginResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User refresh token response
 */
export type PayloadJobRefreshTokenResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User OTP request
 */
export type PayloadLockedDocumentOtpRequestRequest = {
    email: string;
};

/**
 * User OTP login request
 */
export type PayloadLockedDocumentOtpLoginRequest = {
    email: string;
    otp: string;
};

/**
 * User OTP request response
 */
export type PayloadLockedDocumentOtpRequestResponse = {
    type: string;
    message: string;
    value: string;
};

/**
 * User OTP login response
 */
export type PayloadLockedDocumentOtpLoginResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User refresh token response
 */
export type PayloadLockedDocumentRefreshTokenResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User OTP request
 */
export type PayloadPreferenceOtpRequestRequest = {
    email: string;
};

/**
 * User OTP login request
 */
export type PayloadPreferenceOtpLoginRequest = {
    email: string;
    otp: string;
};

/**
 * User OTP request response
 */
export type PayloadPreferenceOtpRequestResponse = {
    type: string;
    message: string;
    value: string;
};

/**
 * User OTP login response
 */
export type PayloadPreferenceOtpLoginResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User refresh token response
 */
export type PayloadPreferenceRefreshTokenResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User OTP request
 */
export type PayloadMigrationOtpRequestRequest = {
    email: string;
};

/**
 * User OTP login request
 */
export type PayloadMigrationOtpLoginRequest = {
    email: string;
    otp: string;
};

/**
 * User OTP request response
 */
export type PayloadMigrationOtpRequestResponse = {
    type: string;
    message: string;
    value: string;
};

/**
 * User OTP login response
 */
export type PayloadMigrationOtpLoginResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User refresh token response
 */
export type PayloadMigrationRefreshTokenResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * Header
 */
export type Header = {
    id: string;
    navItems?: Array<{
        link: {
            type?: 'reference' | 'custom';
            newTab?: boolean | null;
            reference?: {
                relationTo: 'pages';
                value: string | Page;
            } | null | {
                relationTo: 'posts';
                value: string | Post;
            };
            url?: string | null;
            label: string;
        };
        id?: string | null;
    }> | null;
    updatedAt?: string | null;
    createdAt?: string | null;
};

/**
 * Header (if present)
 */
export type HeaderRead = {
    id: string;
    navItems?: Array<{
        link: {
            type?: 'reference' | 'custom';
            newTab?: boolean | null;
            reference?: {
                relationTo: 'pages';
                value: string | Page;
            } | null | {
                relationTo: 'posts';
                value: string | Post;
            };
            url?: string | null;
            label: string;
        };
        id?: string | null;
    }> | null;
    updatedAt?: string | null;
    createdAt?: string | null;
} | {
    [key: string]: unknown;
};

/**
 * Header (writable fields)
 */
export type HeaderWrite = {
    navItems?: Array<{
        link: {
            type?: 'reference' | 'custom';
            newTab?: boolean | null;
            reference?: {
                relationTo: 'pages';
                value: string | Page;
            } | null | {
                relationTo: 'posts';
                value: string | Post;
            };
            url?: string | null;
            label: string;
        };
        id?: string | null;
    }> | null;
};

/**
 * Footer
 */
export type Footer = {
    id: string;
    navItems?: Array<{
        link: {
            type?: 'reference' | 'custom';
            newTab?: boolean | null;
            reference?: {
                relationTo: 'pages';
                value: string | Page;
            } | null | {
                relationTo: 'posts';
                value: string | Post;
            };
            url?: string | null;
            label: string;
        };
        id?: string | null;
    }> | null;
    updatedAt?: string | null;
    createdAt?: string | null;
};

/**
 * Footer (if present)
 */
export type FooterRead = {
    id: string;
    navItems?: Array<{
        link: {
            type?: 'reference' | 'custom';
            newTab?: boolean | null;
            reference?: {
                relationTo: 'pages';
                value: string | Page;
            } | null | {
                relationTo: 'posts';
                value: string | Post;
            };
            url?: string | null;
            label: string;
        };
        id?: string | null;
    }> | null;
    updatedAt?: string | null;
    createdAt?: string | null;
} | {
    [key: string]: unknown;
};

/**
 * Footer (writable fields)
 */
export type FooterWrite = {
    navItems?: Array<{
        link: {
            type?: 'reference' | 'custom';
            newTab?: boolean | null;
            reference?: {
                relationTo: 'pages';
                value: string | Page;
            } | null | {
                relationTo: 'posts';
                value: string | Post;
            };
            url?: string | null;
            label: string;
        };
        id?: string | null;
    }> | null;
};

/**
 * Admin
 * Admin
 */
export type AdminRequestBody = {
    name: string;
    role: 'editor' | 'admin';
    email: string;
    resetPasswordToken?: string | null;
    resetPasswordExpiration?: string | null;
    salt?: string | null;
    hash?: string | null;
    loginAttempts?: number | null;
    lockUntil?: string | null;
    password?: string | null;
};

/**
 * Page
 * Page
 */
export type PageRequestBody = {
    title: string;
    hero: {
        type: 'none' | 'highImpact' | 'mediumImpact' | 'lowImpact';
        richText?: {
            root: {
                type: string;
                children: Array<{
                    type: string;
                    version: number;
                    [key: string]: unknown | string | number;
                }>;
                direction: 'ltr' | 'rtl';
                format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
                indent: number;
                version: number;
            };
        } | null;
        links?: Array<{
            link: {
                type?: 'reference' | 'custom';
                newTab?: boolean | null;
                reference?: {
                    relationTo: 'pages';
                    value: string | Page;
                } | null | {
                    relationTo: 'posts';
                    value: string | Post;
                };
                url?: string | null;
                label: string;
                /**
                 * Choose how the link should be rendered.
                 */
                appearance?: 'default' | 'outline';
            };
            id?: string | null;
        }> | null;
        media?: string | null | Media;
    };
    layout: Array<{
        richText?: {
            root: {
                type: string;
                children: Array<{
                    type: string;
                    version: number;
                    [key: string]: unknown | string | number;
                }>;
                direction: 'ltr' | 'rtl';
                format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
                indent: number;
                version: number;
            };
        } | null;
        links?: Array<{
            link: {
                type?: 'reference' | 'custom';
                newTab?: boolean | null;
                reference?: {
                    relationTo: 'pages';
                    value: string | Page;
                } | null | {
                    relationTo: 'posts';
                    value: string | Post;
                };
                url?: string | null;
                label: string;
                /**
                 * Choose how the link should be rendered.
                 */
                appearance?: 'default' | 'outline';
            };
            id?: string | null;
        }> | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'cta';
    } | {
        columns?: Array<{
            size?: 'oneThird' | 'half' | 'twoThirds' | 'full';
            richText?: {
                root: {
                    type: string;
                    children: Array<{
                        type: string;
                        version: number;
                        [key: string]: unknown | string | number;
                    }>;
                    direction: 'ltr' | 'rtl';
                    format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
                    indent: number;
                    version: number;
                };
            } | null;
            enableLink?: boolean | null;
            link?: {
                type?: 'reference' | 'custom';
                newTab?: boolean | null;
                reference?: {
                    relationTo: 'pages';
                    value: string | Page;
                } | null | {
                    relationTo: 'posts';
                    value: string | Post;
                };
                url?: string | null;
                label: string;
                /**
                 * Choose how the link should be rendered.
                 */
                appearance?: 'default' | 'outline';
            };
            id?: string | null;
        }> | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'content';
    } | {
        media: string | Media;
        id?: string | null;
        blockName?: string | null;
        blockType: 'mediaBlock';
    } | {
        introContent?: {
            root: {
                type: string;
                children: Array<{
                    type: string;
                    version: number;
                    [key: string]: unknown | string | number;
                }>;
                direction: 'ltr' | 'rtl';
                format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
                indent: number;
                version: number;
            };
        } | null;
        populateBy?: 'collection' | 'selection';
        relationTo?: 'posts';
        categories?: Array<string | Category> | null;
        limit?: number | null;
        selectedDocs?: Array<string | Post> | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'archive';
    } | {
        form: string | Form;
        enableIntro?: boolean | null;
        introContent?: {
            root: {
                type: string;
                children: Array<{
                    type: string;
                    version: number;
                    [key: string]: unknown | string | number;
                }>;
                direction: 'ltr' | 'rtl';
                format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
                indent: number;
                version: number;
            };
        } | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'formBlock';
    }>;
    meta?: {
        title?: string | null;
        /**
         * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
         */
        image?: string | null | Media;
        description?: string | null;
    };
    publishedAt?: string | null;
    slug?: string | null;
    slugLock?: boolean | null;
    _status?: 'draft' | 'published';
};

/**
 * Post
 * Post
 */
export type PostRequestBody = {
    title: string;
    heroImage?: string | null | Media;
    content: {
        root: {
            type: string;
            children: Array<{
                type: string;
                version: number;
                [key: string]: unknown | string | number;
            }>;
            direction: 'ltr' | 'rtl';
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
        };
    };
    relatedPosts?: Array<string | Post> | null;
    categories?: Array<string | Category> | null;
    meta?: {
        title?: string | null;
        /**
         * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
         */
        image?: string | null | Media;
        description?: string | null;
    };
    publishedAt?: string | null;
    /**
     * ID of the users
     */
    authors?: string;
    populatedAuthors?: Array<{
        id?: string | null;
        name?: string | null;
    }> | null;
    slug?: string | null;
    slugLock?: boolean | null;
    _status?: 'draft' | 'published';
};

/**
 * Media
 * Media
 */
export type MediaRequestBody = {
    alt?: string | null;
    caption?: {
        root: {
            type: string;
            children: Array<{
                type: string;
                version: number;
                [key: string]: unknown | string | number;
            }>;
            direction: 'ltr' | 'rtl';
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
        };
    } | null;
    prefix?: string | null;
    url?: string | null;
    thumbnailURL?: string | null;
    filename?: string | null;
    mimeType?: string | null;
    filesize?: number | null;
    width?: number | null;
    height?: number | null;
    focalX?: number | null;
    focalY?: number | null;
    sizes?: {
        thumbnail?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
        square?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
        small?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
        medium?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
        large?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
        xlarge?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
        og?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
    };
};

/**
 * Category
 * Category
 */
export type CategoryRequestBody = {
    title: string;
    slug?: string | null;
    slugLock?: boolean | null;
    /**
     * ID of the categories
     */
    parent?: string;
    breadcrumbs?: Array<{
        doc?: string | null | Category;
        url?: string | null;
        label?: string | null;
        id?: string | null;
    }> | null;
};

/**
 * User
 * User
 */
export type UserRequestBody = {
    email: string;
    firstName?: string | null;
    lastName?: string | null;
    /**
     * Upload a profile image for the user
     */
    profileImage?: string | null | Media;
    /**
     * Social accounts for the user
     */
    socialAccounts?: Array<{
        provider: string;
        providerId: string;
        isActive?: boolean | null;
        linkedAt?: string | null;
        id?: string | null;
    }> | null;
    teams?: Array<{
        team: string | Team;
        roles: Array<'teamadmin' | 'teamviewer'>;
        id?: string | null;
    }> | null;
    _otp?: string | null;
    _otpExpiration?: string | null;
};

/**
 * Team
 * Team
 */
export type TeamRequestBody = {
    name: string;
    image?: string | null | Media;
    /**
     * ID of the users
     */
    user: string;
};

/**
 * Project
 * Project
 */
export type ProjectRequestBody = {
    /**
     * ID of the teams
     */
    team?: string;
    name: string;
    domain: string;
    projectGoal?: string | null;
    competitorsDomains?: Array<{
        domain: string;
        id?: string | null;
    }> | null;
    image?: string | null | Media;
    /**
     * ID of the users
     */
    user: string;
    keywordsCount?: number | null;
};

/**
 * Keyword
 * Keyword
 */
export type KeywordRequestBody = {
    keyword: string;
    location: string;
    refreshInterval: 'hourly' | 'daily' | 'weekly' | 'monthly';
    device: Array<'desktop' | 'mobile'>;
    tags?: Array<{
        tag?: string | null;
        id?: string | null;
    }> | null;
    analytics?: Array<{
        rankDistrubution?: number | null;
        serpFeatures?: number | null;
        volatility?: number | null;
        id?: string | null;
    }> | null;
    /**
     * ID of the projects
     */
    project: string;
    /**
     * ID of the users
     */
    user: string;
};

/**
 * Asset
 * Asset
 */
export type AssetRequestBody = {
    /**
     * ID of the teams
     */
    team?: string;
    alt?: string | null;
    caption?: {
        root: {
            type: string;
            children: Array<{
                type: string;
                version: number;
                [key: string]: unknown | string | number;
            }>;
            direction: 'ltr' | 'rtl';
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
        };
    } | null;
    prefix?: string | null;
    url?: string | null;
    thumbnailURL?: string | null;
    filename?: string | null;
    mimeType?: string | null;
    filesize?: number | null;
    width?: number | null;
    height?: number | null;
    focalX?: number | null;
    focalY?: number | null;
    sizes?: {
        thumbnail?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
    };
};

/**
 * Screenshot
 * Screenshot
 */
export type ScreenshotRequestBody = {
    /**
     * ID of the teams
     */
    team?: string;
    name: string;
    /**
     * ID of the users
     */
    user: string;
    /**
     * ID of the assets
     */
    image: string;
};

/**
 * Product
 * Product
 */
export type ProductRequestBody = {
    name?: string | null;
    description?: string | null;
    stripeID?: string | null;
    skipSync?: boolean | null;
};

/**
 * Subscription
 * Subscription
 */
export type SubscriptionRequestBody = {
    /**
     * ID of the teams
     */
    team?: string;
    subscriptionId: string;
};

/**
 * Session
 * Session
 */
export type SessionRequestBody = {
    familyId: string;
    userId: string;
    token: string;
    expiresAt: string;
    ipAddress?: string | null;
    userAgent?: string | null;
};

/**
 * Redirect
 * Redirect
 */
export type RedirectRequestBody = {
    /**
     * You will need to rebuild the website when changing this field.
     */
    from: string;
    to?: {
        type?: 'reference' | 'custom';
        reference?: {
            relationTo: 'pages';
            value: string | Page;
        } | null | {
            relationTo: 'posts';
            value: string | Post;
        };
        url?: string | null;
    };
};

/**
 * Form
 * Form
 */
export type FormRequestBody = {
    title: string;
    fields?: Array<{
        name: string;
        label?: string | null;
        width?: number | null;
        required?: boolean | null;
        defaultValue?: boolean | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'checkbox';
    } | {
        name: string;
        label?: string | null;
        width?: number | null;
        required?: boolean | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'country';
    } | {
        name: string;
        label?: string | null;
        width?: number | null;
        required?: boolean | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'email';
    } | {
        message?: {
            root: {
                type: string;
                children: Array<{
                    type: string;
                    version: number;
                    [key: string]: unknown | string | number;
                }>;
                direction: 'ltr' | 'rtl';
                format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
                indent: number;
                version: number;
            };
        } | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'message';
    } | {
        name: string;
        label?: string | null;
        width?: number | null;
        defaultValue?: number | null;
        required?: boolean | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'number';
    } | {
        name: string;
        label?: string | null;
        width?: number | null;
        defaultValue?: string | null;
        placeholder?: string | null;
        options?: Array<{
            label: string;
            value: string;
            id?: string | null;
        }> | null;
        required?: boolean | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'select';
    } | {
        name: string;
        label?: string | null;
        width?: number | null;
        required?: boolean | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'state';
    } | {
        name: string;
        label?: string | null;
        width?: number | null;
        defaultValue?: string | null;
        required?: boolean | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'text';
    } | {
        name: string;
        label?: string | null;
        width?: number | null;
        defaultValue?: string | null;
        required?: boolean | null;
        id?: string | null;
        blockName?: string | null;
        blockType: 'textarea';
    }> | null;
    submitButtonLabel?: string | null;
    /**
     * Choose whether to display an on-page message or redirect to a different page after they submit the form.
     */
    confirmationType?: 'message' | 'redirect';
    confirmationMessage?: {
        root: {
            type: string;
            children: Array<{
                type: string;
                version: number;
                [key: string]: unknown | string | number;
            }>;
            direction: 'ltr' | 'rtl';
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
        };
    } | null;
    redirect?: {
        url: string;
    };
    /**
     * Send custom emails when the form submits. Use comma separated lists to send the same email to multiple recipients. To reference a value from this form, wrap that field's name with double curly brackets, i.e. {{firstName}}. You can use a wildcard {{*}} to output all data and {{*:table}} to format it as an HTML table in the email.
     */
    emails?: Array<{
        emailTo?: string | null;
        cc?: string | null;
        bcc?: string | null;
        replyTo?: string | null;
        emailFrom?: string | null;
        subject: string;
        /**
         * Enter the message that should be sent in this email.
         */
        message?: {
            root: {
                type: string;
                children: Array<{
                    type: string;
                    version: number;
                    [key: string]: unknown | string | number;
                }>;
                direction: 'ltr' | 'rtl';
                format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
                indent: number;
                version: number;
            };
        } | null;
        id?: string | null;
    }> | null;
};

/**
 * FormSubmission
 * Form Submission
 */
export type FormSubmissionRequestBody = {
    /**
     * ID of the forms
     */
    form: string;
    submissionData?: Array<{
        field: string;
        value: string;
        id?: string | null;
    }> | null;
};

/**
 * Search
 * This is a collection of automatically created search results. These results are used by the global site search and will be updated automatically as documents in the CMS are created or updated.
 */
export type SearchResultRequestBody = {
    title?: string | null;
    priority?: number | null;
    /**
     * ID of the posts
     */
    doc: string;
    slug?: string | null;
    meta?: {
        title?: string | null;
        description?: string | null;
        image?: string | null | Media;
    };
    categories?: Array<{
        relationTo?: string | null;
        categoryID?: string | null;
        title?: string | null;
        id?: string | null;
    }> | null;
};

/**
 * PayloadJob
 * Payload Job
 */
export type PayloadJobRequestBody = {
    /**
     * Input data provided to the job
     */
    input?: {
        [key: string]: unknown;
    } | Array<unknown> | string | number | boolean | null;
    taskStatus?: {
        [key: string]: unknown;
    } | Array<unknown> | string | number | boolean | null;
    completedAt?: string | null;
    totalTried?: number | null;
    /**
     * If hasError is true this job will not be retried
     */
    hasError?: boolean | null;
    /**
     * If hasError is true, this is the error that caused it
     */
    error?: {
        [key: string]: unknown;
    } | Array<unknown> | string | number | boolean | null;
    /**
     * Task execution log
     */
    log?: Array<{
        executedAt: string;
        completedAt: string;
        taskSlug: 'inline' | 'schedulePublish';
        taskID: string;
        input?: {
            [key: string]: unknown;
        } | Array<unknown> | string | number | boolean | null;
        output?: {
            [key: string]: unknown;
        } | Array<unknown> | string | number | boolean | null;
        state: 'failed' | 'succeeded';
        error?: {
            [key: string]: unknown;
        } | Array<unknown> | string | number | boolean | null;
        id?: string | null;
    }> | null;
    taskSlug?: 'inline' | 'schedulePublish';
    queue?: string | null;
    waitUntil?: string | null;
    processing?: boolean | null;
};

/**
 * PayloadLockedDocument
 * Payload Locked Document
 */
export type PayloadLockedDocumentRequestBody = {
    /**
     * ID of the admins/pages/posts/media/categories/users/teams/projects/keywords/assets/screenshots/products/subscriptions/sessions/redirects/forms/form-submissions/search/payload-jobs
     */
    document?: string;
    globalSlug?: string | null;
    /**
     * ID of the admins/users
     */
    user: string;
};

/**
 * PayloadPreference
 * Payload Preference
 */
export type PayloadPreferenceRequestBody = {
    /**
     * ID of the admins/users
     */
    user: string;
    key?: string | null;
    value?: {
        [key: string]: unknown;
    } | Array<unknown> | string | number | boolean | null;
};

/**
 * PayloadMigration
 * Payload Migration
 */
export type PayloadMigrationRequestBody = {
    name?: string | null;
    batch?: number | null;
};

export type AdminLoginRequestBody = AdminLoginRequest;

export type AdminForgotPasswordRequestBody = AdminForgotPasswordRequest;

export type AdminResetPasswordRequestBody = AdminResetPasswordRequest;

export type AdminUnlockRequestBody = AdminUnlockRequest;

export type AdminOtpRequestRequestBody = AdminOtpRequestRequest;

export type AdminOtpLoginRequestBody = AdminOtpLoginRequest;

export type PageOtpRequestRequestBody = PageOtpRequestRequest;

export type PageOtpLoginRequestBody = PageOtpLoginRequest;

export type PostOtpRequestRequestBody = PostOtpRequestRequest;

export type PostOtpLoginRequestBody = PostOtpLoginRequest;

export type MediaOtpRequestRequestBody = MediaOtpRequestRequest;

export type MediaOtpLoginRequestBody = MediaOtpLoginRequest;

export type CategoryOtpRequestRequestBody = CategoryOtpRequestRequest;

export type CategoryOtpLoginRequestBody = CategoryOtpLoginRequest;

export type UserOtpRequestRequestBody = UserOtpRequestRequest;

export type UserOtpLoginRequestBody = UserOtpLoginRequest;

export type TeamOtpRequestRequestBody = TeamOtpRequestRequest;

export type TeamOtpLoginRequestBody = TeamOtpLoginRequest;

export type ProjectOtpRequestRequestBody = ProjectOtpRequestRequest;

export type ProjectOtpLoginRequestBody = ProjectOtpLoginRequest;

export type KeywordOtpRequestRequestBody = KeywordOtpRequestRequest;

export type KeywordOtpLoginRequestBody = KeywordOtpLoginRequest;

export type AssetOtpRequestRequestBody = AssetOtpRequestRequest;

export type AssetOtpLoginRequestBody = AssetOtpLoginRequest;

export type ScreenshotOtpRequestRequestBody = ScreenshotOtpRequestRequest;

export type ScreenshotOtpLoginRequestBody = ScreenshotOtpLoginRequest;

export type ProductOtpRequestRequestBody = ProductOtpRequestRequest;

export type ProductOtpLoginRequestBody = ProductOtpLoginRequest;

export type SubscriptionOtpRequestRequestBody = SubscriptionOtpRequestRequest;

export type SubscriptionOtpLoginRequestBody = SubscriptionOtpLoginRequest;

export type SessionOtpRequestRequestBody = SessionOtpRequestRequest;

export type SessionOtpLoginRequestBody = SessionOtpLoginRequest;

export type RedirectOtpRequestRequestBody = RedirectOtpRequestRequest;

export type RedirectOtpLoginRequestBody = RedirectOtpLoginRequest;

export type FormOtpRequestRequestBody = FormOtpRequestRequest;

export type FormOtpLoginRequestBody = FormOtpLoginRequest;

export type FormSubmissionOtpRequestRequestBody = FormSubmissionOtpRequestRequest;

export type FormSubmissionOtpLoginRequestBody = FormSubmissionOtpLoginRequest;

export type SearchResultOtpRequestRequestBody = SearchResultOtpRequestRequest;

export type SearchResultOtpLoginRequestBody = SearchResultOtpLoginRequest;

export type PayloadJobOtpRequestRequestBody = PayloadJobOtpRequestRequest;

export type PayloadJobOtpLoginRequestBody = PayloadJobOtpLoginRequest;

export type PayloadLockedDocumentOtpRequestRequestBody = PayloadLockedDocumentOtpRequestRequest;

export type PayloadLockedDocumentOtpLoginRequestBody = PayloadLockedDocumentOtpLoginRequest;

export type PayloadPreferenceOtpRequestRequestBody = PayloadPreferenceOtpRequestRequest;

export type PayloadPreferenceOtpLoginRequestBody = PayloadPreferenceOtpLoginRequest;

export type PayloadMigrationOtpRequestRequestBody = PayloadMigrationOtpRequestRequest;

export type PayloadMigrationOtpLoginRequestBody = PayloadMigrationOtpLoginRequest;

export type HeaderRequestBody = HeaderWrite;

export type FooterRequestBody = FooterWrite;

export type GetApiAdminsData = {
    body?: never;
    path?: never;
    query?: {
        page?: number;
        limit?: number;
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
        sort?: 'id' | '-id' | 'name' | '-name' | 'updatedAt' | '-updatedAt' | 'createdAt' | '-createdAt' | 'email' | '-email' | 'resetPasswordToken' | '-resetPasswordToken' | 'resetPasswordExpiration' | '-resetPasswordExpiration' | 'salt' | '-salt' | 'hash' | '-hash' | 'loginAttempts' | '-loginAttempts' | 'lockUntil' | '-lockUntil';
        where?: {
            [key: string]: unknown;
        } & (AdminQueryOperations | AdminQueryOperationsAnd | AdminQueryOperationsOr);
    };
    url: '/api/admins';
};

export type GetApiAdminsResponses = {
    /**
     * List of Admins
     */
    200: {
        docs: Array<Admin>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage: number | null;
        nextPage: number | null;
    };
};

export type GetApiAdminsResponse = GetApiAdminsResponses[keyof GetApiAdminsResponses];

export type PostApiAdminsData = {
    /**
     * Admin
     */
    body?: AdminRequestBody;
    path?: never;
    query?: never;
    url: '/api/admins';
};

export type PostApiAdminsResponses = {
    /**
     * Admin object
     */
    201: {
        message: string;
        doc: Admin & {
            id: string;
            createdAt: string;
            updatedAt: string;
        };
    };
};

export type PostApiAdminsResponse = PostApiAdminsResponses[keyof PostApiAdminsResponses];

export type DeleteApiAdminsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Admin
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/admins/{id}';
};

export type DeleteApiAdminsByIdErrors = {
    /**
     * Admin not found
     */
    404: unknown;
};

export type DeleteApiAdminsByIdResponses = {
    /**
     * Admin object
     */
    200: Admin;
};

export type DeleteApiAdminsByIdResponse = DeleteApiAdminsByIdResponses[keyof DeleteApiAdminsByIdResponses];

export type GetApiAdminsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Admin
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/admins/{id}';
};

export type GetApiAdminsByIdErrors = {
    /**
     * Admin not found
     */
    404: unknown;
};

export type GetApiAdminsByIdResponses = {
    /**
     * Admin object
     */
    200: Admin;
};

export type GetApiAdminsByIdResponse = GetApiAdminsByIdResponses[keyof GetApiAdminsByIdResponses];

export type PatchApiAdminsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Admin
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/admins/{id}';
};

export type PatchApiAdminsByIdErrors = {
    /**
     * Admin not found
     */
    404: unknown;
};

export type PatchApiAdminsByIdResponses = {
    /**
     * Admin object
     */
    200: Admin;
};

export type PatchApiAdminsByIdResponse = PatchApiAdminsByIdResponses[keyof PatchApiAdminsByIdResponses];

export type GetApiPagesData = {
    body?: never;
    path?: never;
    query?: {
        page?: number;
        limit?: number;
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
        sort?: 'title' | '-title' | 'publishedAt' | '-publishedAt' | 'slug' | '-slug' | 'updatedAt' | '-updatedAt' | 'createdAt' | '-createdAt';
        where?: {
            [key: string]: unknown;
        } & (PageQueryOperations | PageQueryOperationsAnd | PageQueryOperationsOr);
    };
    url: '/api/pages';
};

export type GetApiPagesResponses = {
    /**
     * List of Pages
     */
    200: {
        docs: Array<Page>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage: number | null;
        nextPage: number | null;
    };
};

export type GetApiPagesResponse = GetApiPagesResponses[keyof GetApiPagesResponses];

export type PostApiPagesData = {
    /**
     * Page
     */
    body?: PageRequestBody;
    path?: never;
    query?: never;
    url: '/api/pages';
};

export type PostApiPagesResponses = {
    /**
     * Page object
     */
    201: {
        message: string;
        doc: Page & {
            id: string;
            createdAt: string;
            updatedAt: string;
        };
    };
};

export type PostApiPagesResponse = PostApiPagesResponses[keyof PostApiPagesResponses];

export type DeleteApiPagesByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Page
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/pages/{id}';
};

export type DeleteApiPagesByIdErrors = {
    /**
     * Page not found
     */
    404: unknown;
};

export type DeleteApiPagesByIdResponses = {
    /**
     * Page object
     */
    200: Page;
};

export type DeleteApiPagesByIdResponse = DeleteApiPagesByIdResponses[keyof DeleteApiPagesByIdResponses];

export type GetApiPagesByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Page
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/pages/{id}';
};

export type GetApiPagesByIdErrors = {
    /**
     * Page not found
     */
    404: unknown;
};

export type GetApiPagesByIdResponses = {
    /**
     * Page object
     */
    200: Page;
};

export type GetApiPagesByIdResponse = GetApiPagesByIdResponses[keyof GetApiPagesByIdResponses];

export type PatchApiPagesByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Page
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/pages/{id}';
};

export type PatchApiPagesByIdErrors = {
    /**
     * Page not found
     */
    404: unknown;
};

export type PatchApiPagesByIdResponses = {
    /**
     * Page object
     */
    200: Page;
};

export type PatchApiPagesByIdResponse = PatchApiPagesByIdResponses[keyof PatchApiPagesByIdResponses];

export type GetApiPostsData = {
    body?: never;
    path?: never;
    query?: {
        page?: number;
        limit?: number;
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
        sort?: 'title' | '-title' | 'publishedAt' | '-publishedAt' | 'slug' | '-slug' | 'updatedAt' | '-updatedAt' | 'createdAt' | '-createdAt';
        where?: {
            [key: string]: unknown;
        } & (PostQueryOperations | PostQueryOperationsAnd | PostQueryOperationsOr);
    };
    url: '/api/posts';
};

export type GetApiPostsResponses = {
    /**
     * List of Posts
     */
    200: {
        docs: Array<Post>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage: number | null;
        nextPage: number | null;
    };
};

export type GetApiPostsResponse = GetApiPostsResponses[keyof GetApiPostsResponses];

export type PostApiPostsData = {
    /**
     * Post
     */
    body?: PostRequestBody;
    path?: never;
    query?: never;
    url: '/api/posts';
};

export type PostApiPostsResponses = {
    /**
     * Post object
     */
    201: {
        message: string;
        doc: Post & {
            id: string;
            createdAt: string;
            updatedAt: string;
        };
    };
};

export type PostApiPostsResponse = PostApiPostsResponses[keyof PostApiPostsResponses];

export type DeleteApiPostsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Post
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/posts/{id}';
};

export type DeleteApiPostsByIdErrors = {
    /**
     * Post not found
     */
    404: unknown;
};

export type DeleteApiPostsByIdResponses = {
    /**
     * Post object
     */
    200: Post;
};

export type DeleteApiPostsByIdResponse = DeleteApiPostsByIdResponses[keyof DeleteApiPostsByIdResponses];

export type GetApiPostsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Post
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/posts/{id}';
};

export type GetApiPostsByIdErrors = {
    /**
     * Post not found
     */
    404: unknown;
};

export type GetApiPostsByIdResponses = {
    /**
     * Post object
     */
    200: Post;
};

export type GetApiPostsByIdResponse = GetApiPostsByIdResponses[keyof GetApiPostsByIdResponses];

export type PatchApiPostsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Post
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/posts/{id}';
};

export type PatchApiPostsByIdErrors = {
    /**
     * Post not found
     */
    404: unknown;
};

export type PatchApiPostsByIdResponses = {
    /**
     * Post object
     */
    200: Post;
};

export type PatchApiPostsByIdResponse = PatchApiPostsByIdResponses[keyof PatchApiPostsByIdResponses];

export type GetApiMediaData = {
    body?: never;
    path?: never;
    query?: {
        page?: number;
        limit?: number;
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
        sort?: 'alt' | '-alt' | 'prefix' | '-prefix' | 'updatedAt' | '-updatedAt' | 'createdAt' | '-createdAt' | 'url' | '-url' | 'thumbnailURL' | '-thumbnailURL' | 'filename' | '-filename' | 'mimeType' | '-mimeType' | 'filesize' | '-filesize' | 'width' | '-width' | 'height' | '-height' | 'focalX' | '-focalX' | 'focalY' | '-focalY';
        where?: {
            [key: string]: unknown;
        } & (MediaQueryOperations | MediaQueryOperationsAnd | MediaQueryOperationsOr);
    };
    url: '/api/media';
};

export type GetApiMediaResponses = {
    /**
     * List of Media
     */
    200: {
        docs: Array<Media>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage: number | null;
        nextPage: number | null;
    };
};

export type GetApiMediaResponse = GetApiMediaResponses[keyof GetApiMediaResponses];

export type PostApiMediaData = {
    /**
     * Media
     */
    body?: MediaRequestBody;
    path?: never;
    query?: never;
    url: '/api/media';
};

export type PostApiMediaResponses = {
    /**
     * Media object
     */
    201: {
        message: string;
        doc: Media & {
            id: string;
            createdAt: string;
            updatedAt: string;
        };
    };
};

export type PostApiMediaResponse = PostApiMediaResponses[keyof PostApiMediaResponses];

export type DeleteApiMediaByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Media
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/media/{id}';
};

export type DeleteApiMediaByIdErrors = {
    /**
     * Media not found
     */
    404: unknown;
};

export type DeleteApiMediaByIdResponses = {
    /**
     * Media object
     */
    200: Media;
};

export type DeleteApiMediaByIdResponse = DeleteApiMediaByIdResponses[keyof DeleteApiMediaByIdResponses];

export type GetApiMediaByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Media
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/media/{id}';
};

export type GetApiMediaByIdErrors = {
    /**
     * Media not found
     */
    404: unknown;
};

export type GetApiMediaByIdResponses = {
    /**
     * Media object
     */
    200: Media;
};

export type GetApiMediaByIdResponse = GetApiMediaByIdResponses[keyof GetApiMediaByIdResponses];

export type PatchApiMediaByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Media
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/media/{id}';
};

export type PatchApiMediaByIdErrors = {
    /**
     * Media not found
     */
    404: unknown;
};

export type PatchApiMediaByIdResponses = {
    /**
     * Media object
     */
    200: Media;
};

export type PatchApiMediaByIdResponse = PatchApiMediaByIdResponses[keyof PatchApiMediaByIdResponses];

export type GetApiCategoriesData = {
    body?: never;
    path?: never;
    query?: {
        page?: number;
        limit?: number;
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
        sort?: 'title' | '-title' | 'slug' | '-slug' | 'updatedAt' | '-updatedAt' | 'createdAt' | '-createdAt';
        where?: {
            [key: string]: unknown;
        } & (CategoryQueryOperations | CategoryQueryOperationsAnd | CategoryQueryOperationsOr);
    };
    url: '/api/categories';
};

export type GetApiCategoriesResponses = {
    /**
     * List of Categories
     */
    200: {
        docs: Array<Category>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage: number | null;
        nextPage: number | null;
    };
};

export type GetApiCategoriesResponse = GetApiCategoriesResponses[keyof GetApiCategoriesResponses];

export type PostApiCategoriesData = {
    /**
     * Category
     */
    body?: CategoryRequestBody;
    path?: never;
    query?: never;
    url: '/api/categories';
};

export type PostApiCategoriesResponses = {
    /**
     * Category object
     */
    201: {
        message: string;
        doc: Category & {
            id: string;
            createdAt: string;
            updatedAt: string;
        };
    };
};

export type PostApiCategoriesResponse = PostApiCategoriesResponses[keyof PostApiCategoriesResponses];

export type DeleteApiCategoriesByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Category
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/categories/{id}';
};

export type DeleteApiCategoriesByIdErrors = {
    /**
     * Category not found
     */
    404: unknown;
};

export type DeleteApiCategoriesByIdResponses = {
    /**
     * Category object
     */
    200: Category;
};

export type DeleteApiCategoriesByIdResponse = DeleteApiCategoriesByIdResponses[keyof DeleteApiCategoriesByIdResponses];

export type GetApiCategoriesByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Category
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/categories/{id}';
};

export type GetApiCategoriesByIdErrors = {
    /**
     * Category not found
     */
    404: unknown;
};

export type GetApiCategoriesByIdResponses = {
    /**
     * Category object
     */
    200: Category;
};

export type GetApiCategoriesByIdResponse = GetApiCategoriesByIdResponses[keyof GetApiCategoriesByIdResponses];

export type PatchApiCategoriesByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Category
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/categories/{id}';
};

export type PatchApiCategoriesByIdErrors = {
    /**
     * Category not found
     */
    404: unknown;
};

export type PatchApiCategoriesByIdResponses = {
    /**
     * Category object
     */
    200: Category;
};

export type PatchApiCategoriesByIdResponse = PatchApiCategoriesByIdResponses[keyof PatchApiCategoriesByIdResponses];

export type GetApiUsersData = {
    body?: never;
    path?: never;
    query?: {
        page?: number;
        limit?: number;
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
        sort?: 'id' | '-id' | 'email' | '-email' | 'firstName' | '-firstName' | 'lastName' | '-lastName' | '_otp' | '-_otp' | '_otpExpiration' | '-_otpExpiration' | 'updatedAt' | '-updatedAt' | 'createdAt' | '-createdAt';
        where?: {
            [key: string]: unknown;
        } & (UserQueryOperations | UserQueryOperationsAnd | UserQueryOperationsOr);
    };
    url: '/api/users';
};

export type GetApiUsersResponses = {
    /**
     * List of Users
     */
    200: {
        docs: Array<User>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage: number | null;
        nextPage: number | null;
    };
};

export type GetApiUsersResponse = GetApiUsersResponses[keyof GetApiUsersResponses];

export type PostApiUsersData = {
    /**
     * User
     */
    body?: UserRequestBody;
    path?: never;
    query?: never;
    url: '/api/users';
};

export type PostApiUsersResponses = {
    /**
     * User object
     */
    201: {
        message: string;
        doc: User & {
            id: string;
            createdAt: string;
            updatedAt: string;
        };
    };
};

export type PostApiUsersResponse = PostApiUsersResponses[keyof PostApiUsersResponses];

export type DeleteApiUsersByIdData = {
    body?: never;
    path: {
        /**
         * ID of the User
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/users/{id}';
};

export type DeleteApiUsersByIdErrors = {
    /**
     * User not found
     */
    404: unknown;
};

export type DeleteApiUsersByIdResponses = {
    /**
     * User object
     */
    200: User;
};

export type DeleteApiUsersByIdResponse = DeleteApiUsersByIdResponses[keyof DeleteApiUsersByIdResponses];

export type GetApiUsersByIdData = {
    body?: never;
    path: {
        /**
         * ID of the User
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/users/{id}';
};

export type GetApiUsersByIdErrors = {
    /**
     * User not found
     */
    404: unknown;
};

export type GetApiUsersByIdResponses = {
    /**
     * User object
     */
    200: User;
};

export type GetApiUsersByIdResponse = GetApiUsersByIdResponses[keyof GetApiUsersByIdResponses];

export type PatchApiUsersByIdData = {
    body?: never;
    path: {
        /**
         * ID of the User
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/users/{id}';
};

export type PatchApiUsersByIdErrors = {
    /**
     * User not found
     */
    404: unknown;
};

export type PatchApiUsersByIdResponses = {
    /**
     * User object
     */
    200: User;
};

export type PatchApiUsersByIdResponse = PatchApiUsersByIdResponses[keyof PatchApiUsersByIdResponses];

export type GetApiTeamsData = {
    body?: never;
    path?: never;
    query?: {
        page?: number;
        limit?: number;
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
        sort?: 'name' | '-name' | 'updatedAt' | '-updatedAt' | 'createdAt' | '-createdAt';
        where?: {
            [key: string]: unknown;
        } & (TeamQueryOperations | TeamQueryOperationsAnd | TeamQueryOperationsOr);
    };
    url: '/api/teams';
};

export type GetApiTeamsResponses = {
    /**
     * List of Teams
     */
    200: {
        docs: Array<Team>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage: number | null;
        nextPage: number | null;
    };
};

export type GetApiTeamsResponse = GetApiTeamsResponses[keyof GetApiTeamsResponses];

export type PostApiTeamsData = {
    /**
     * Team
     */
    body?: TeamRequestBody;
    path?: never;
    query?: never;
    url: '/api/teams';
};

export type PostApiTeamsResponses = {
    /**
     * Team object
     */
    201: {
        message: string;
        doc: Team & {
            id: string;
            createdAt: string;
            updatedAt: string;
        };
    };
};

export type PostApiTeamsResponse = PostApiTeamsResponses[keyof PostApiTeamsResponses];

export type DeleteApiTeamsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Team
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/teams/{id}';
};

export type DeleteApiTeamsByIdErrors = {
    /**
     * Team not found
     */
    404: unknown;
};

export type DeleteApiTeamsByIdResponses = {
    /**
     * Team object
     */
    200: Team;
};

export type DeleteApiTeamsByIdResponse = DeleteApiTeamsByIdResponses[keyof DeleteApiTeamsByIdResponses];

export type GetApiTeamsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Team
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/teams/{id}';
};

export type GetApiTeamsByIdErrors = {
    /**
     * Team not found
     */
    404: unknown;
};

export type GetApiTeamsByIdResponses = {
    /**
     * Team object
     */
    200: Team;
};

export type GetApiTeamsByIdResponse = GetApiTeamsByIdResponses[keyof GetApiTeamsByIdResponses];

export type PatchApiTeamsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Team
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/teams/{id}';
};

export type PatchApiTeamsByIdErrors = {
    /**
     * Team not found
     */
    404: unknown;
};

export type PatchApiTeamsByIdResponses = {
    /**
     * Team object
     */
    200: Team;
};

export type PatchApiTeamsByIdResponse = PatchApiTeamsByIdResponses[keyof PatchApiTeamsByIdResponses];

export type GetApiProjectsData = {
    body?: never;
    path?: never;
    query?: {
        page?: number;
        limit?: number;
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
        sort?: 'name' | '-name' | 'domain' | '-domain' | 'keywordsCount' | '-keywordsCount' | 'updatedAt' | '-updatedAt' | 'createdAt' | '-createdAt';
        where?: {
            [key: string]: unknown;
        } & (ProjectQueryOperations | ProjectQueryOperationsAnd | ProjectQueryOperationsOr);
    };
    url: '/api/projects';
};

export type GetApiProjectsResponses = {
    /**
     * List of Projects
     */
    200: {
        docs: Array<Project>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage: number | null;
        nextPage: number | null;
    };
};

export type GetApiProjectsResponse = GetApiProjectsResponses[keyof GetApiProjectsResponses];

export type PostApiProjectsData = {
    /**
     * Project
     */
    body?: ProjectRequestBody;
    path?: never;
    query?: never;
    url: '/api/projects';
};

export type PostApiProjectsResponses = {
    /**
     * Project object
     */
    201: {
        message: string;
        doc: Project & {
            id: string;
            createdAt: string;
            updatedAt: string;
        };
    };
};

export type PostApiProjectsResponse = PostApiProjectsResponses[keyof PostApiProjectsResponses];

export type DeleteApiProjectsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Project
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/projects/{id}';
};

export type DeleteApiProjectsByIdErrors = {
    /**
     * Project not found
     */
    404: unknown;
};

export type DeleteApiProjectsByIdResponses = {
    /**
     * Project object
     */
    200: Project;
};

export type DeleteApiProjectsByIdResponse = DeleteApiProjectsByIdResponses[keyof DeleteApiProjectsByIdResponses];

export type GetApiProjectsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Project
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/projects/{id}';
};

export type GetApiProjectsByIdErrors = {
    /**
     * Project not found
     */
    404: unknown;
};

export type GetApiProjectsByIdResponses = {
    /**
     * Project object
     */
    200: Project;
};

export type GetApiProjectsByIdResponse = GetApiProjectsByIdResponses[keyof GetApiProjectsByIdResponses];

export type PatchApiProjectsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Project
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/projects/{id}';
};

export type PatchApiProjectsByIdErrors = {
    /**
     * Project not found
     */
    404: unknown;
};

export type PatchApiProjectsByIdResponses = {
    /**
     * Project object
     */
    200: Project;
};

export type PatchApiProjectsByIdResponse = PatchApiProjectsByIdResponses[keyof PatchApiProjectsByIdResponses];

export type GetApiKeywordsData = {
    body?: never;
    path?: never;
    query?: {
        page?: number;
        limit?: number;
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
        sort?: 'keyword' | '-keyword' | 'location' | '-location' | 'updatedAt' | '-updatedAt' | 'createdAt' | '-createdAt';
        where?: {
            [key: string]: unknown;
        } & (KeywordQueryOperations | KeywordQueryOperationsAnd | KeywordQueryOperationsOr);
    };
    url: '/api/keywords';
};

export type GetApiKeywordsResponses = {
    /**
     * List of Keywords
     */
    200: {
        docs: Array<Keyword>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage: number | null;
        nextPage: number | null;
    };
};

export type GetApiKeywordsResponse = GetApiKeywordsResponses[keyof GetApiKeywordsResponses];

export type PostApiKeywordsData = {
    /**
     * Keyword
     */
    body?: KeywordRequestBody;
    path?: never;
    query?: never;
    url: '/api/keywords';
};

export type PostApiKeywordsResponses = {
    /**
     * Keyword object
     */
    201: {
        message: string;
        doc: Keyword & {
            id: string;
            createdAt: string;
            updatedAt: string;
        };
    };
};

export type PostApiKeywordsResponse = PostApiKeywordsResponses[keyof PostApiKeywordsResponses];

export type DeleteApiKeywordsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Keyword
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/keywords/{id}';
};

export type DeleteApiKeywordsByIdErrors = {
    /**
     * Keyword not found
     */
    404: unknown;
};

export type DeleteApiKeywordsByIdResponses = {
    /**
     * Keyword object
     */
    200: Keyword;
};

export type DeleteApiKeywordsByIdResponse = DeleteApiKeywordsByIdResponses[keyof DeleteApiKeywordsByIdResponses];

export type GetApiKeywordsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Keyword
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/keywords/{id}';
};

export type GetApiKeywordsByIdErrors = {
    /**
     * Keyword not found
     */
    404: unknown;
};

export type GetApiKeywordsByIdResponses = {
    /**
     * Keyword object
     */
    200: Keyword;
};

export type GetApiKeywordsByIdResponse = GetApiKeywordsByIdResponses[keyof GetApiKeywordsByIdResponses];

export type PatchApiKeywordsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Keyword
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/keywords/{id}';
};

export type PatchApiKeywordsByIdErrors = {
    /**
     * Keyword not found
     */
    404: unknown;
};

export type PatchApiKeywordsByIdResponses = {
    /**
     * Keyword object
     */
    200: Keyword;
};

export type PatchApiKeywordsByIdResponse = PatchApiKeywordsByIdResponses[keyof PatchApiKeywordsByIdResponses];

export type GetApiAssetsData = {
    body?: never;
    path?: never;
    query?: {
        page?: number;
        limit?: number;
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
        sort?: 'alt' | '-alt' | 'prefix' | '-prefix' | 'updatedAt' | '-updatedAt' | 'createdAt' | '-createdAt' | 'url' | '-url' | 'thumbnailURL' | '-thumbnailURL' | 'filename' | '-filename' | 'mimeType' | '-mimeType' | 'filesize' | '-filesize' | 'width' | '-width' | 'height' | '-height' | 'focalX' | '-focalX' | 'focalY' | '-focalY';
        where?: {
            [key: string]: unknown;
        } & (AssetQueryOperations | AssetQueryOperationsAnd | AssetQueryOperationsOr);
    };
    url: '/api/assets';
};

export type GetApiAssetsResponses = {
    /**
     * List of Assets
     */
    200: {
        docs: Array<Asset>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage: number | null;
        nextPage: number | null;
    };
};

export type GetApiAssetsResponse = GetApiAssetsResponses[keyof GetApiAssetsResponses];

export type PostApiAssetsData = {
    /**
     * Asset
     */
    body?: AssetRequestBody;
    path?: never;
    query?: never;
    url: '/api/assets';
};

export type PostApiAssetsResponses = {
    /**
     * Asset object
     */
    201: {
        message: string;
        doc: Asset & {
            id: string;
            createdAt: string;
            updatedAt: string;
        };
    };
};

export type PostApiAssetsResponse = PostApiAssetsResponses[keyof PostApiAssetsResponses];

export type DeleteApiAssetsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Asset
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/assets/{id}';
};

export type DeleteApiAssetsByIdErrors = {
    /**
     * Asset not found
     */
    404: unknown;
};

export type DeleteApiAssetsByIdResponses = {
    /**
     * Asset object
     */
    200: Asset;
};

export type DeleteApiAssetsByIdResponse = DeleteApiAssetsByIdResponses[keyof DeleteApiAssetsByIdResponses];

export type GetApiAssetsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Asset
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/assets/{id}';
};

export type GetApiAssetsByIdErrors = {
    /**
     * Asset not found
     */
    404: unknown;
};

export type GetApiAssetsByIdResponses = {
    /**
     * Asset object
     */
    200: Asset;
};

export type GetApiAssetsByIdResponse = GetApiAssetsByIdResponses[keyof GetApiAssetsByIdResponses];

export type PatchApiAssetsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Asset
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/assets/{id}';
};

export type PatchApiAssetsByIdErrors = {
    /**
     * Asset not found
     */
    404: unknown;
};

export type PatchApiAssetsByIdResponses = {
    /**
     * Asset object
     */
    200: Asset;
};

export type PatchApiAssetsByIdResponse = PatchApiAssetsByIdResponses[keyof PatchApiAssetsByIdResponses];

export type GetApiScreenshotsData = {
    body?: never;
    path?: never;
    query?: {
        page?: number;
        limit?: number;
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
        sort?: 'name' | '-name' | 'updatedAt' | '-updatedAt' | 'createdAt' | '-createdAt';
        where?: {
            [key: string]: unknown;
        } & (ScreenshotQueryOperations | ScreenshotQueryOperationsAnd | ScreenshotQueryOperationsOr);
    };
    url: '/api/screenshots';
};

export type GetApiScreenshotsResponses = {
    /**
     * List of Screenshots
     */
    200: {
        docs: Array<Screenshot>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage: number | null;
        nextPage: number | null;
    };
};

export type GetApiScreenshotsResponse = GetApiScreenshotsResponses[keyof GetApiScreenshotsResponses];

export type PostApiScreenshotsData = {
    /**
     * Screenshot
     */
    body?: ScreenshotRequestBody;
    path?: never;
    query?: never;
    url: '/api/screenshots';
};

export type PostApiScreenshotsResponses = {
    /**
     * Screenshot object
     */
    201: {
        message: string;
        doc: Screenshot & {
            id: string;
            createdAt: string;
            updatedAt: string;
        };
    };
};

export type PostApiScreenshotsResponse = PostApiScreenshotsResponses[keyof PostApiScreenshotsResponses];

export type DeleteApiScreenshotsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Screenshot
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/screenshots/{id}';
};

export type DeleteApiScreenshotsByIdErrors = {
    /**
     * Screenshot not found
     */
    404: unknown;
};

export type DeleteApiScreenshotsByIdResponses = {
    /**
     * Screenshot object
     */
    200: Screenshot;
};

export type DeleteApiScreenshotsByIdResponse = DeleteApiScreenshotsByIdResponses[keyof DeleteApiScreenshotsByIdResponses];

export type GetApiScreenshotsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Screenshot
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/screenshots/{id}';
};

export type GetApiScreenshotsByIdErrors = {
    /**
     * Screenshot not found
     */
    404: unknown;
};

export type GetApiScreenshotsByIdResponses = {
    /**
     * Screenshot object
     */
    200: Screenshot;
};

export type GetApiScreenshotsByIdResponse = GetApiScreenshotsByIdResponses[keyof GetApiScreenshotsByIdResponses];

export type PatchApiScreenshotsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Screenshot
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/screenshots/{id}';
};

export type PatchApiScreenshotsByIdErrors = {
    /**
     * Screenshot not found
     */
    404: unknown;
};

export type PatchApiScreenshotsByIdResponses = {
    /**
     * Screenshot object
     */
    200: Screenshot;
};

export type PatchApiScreenshotsByIdResponse = PatchApiScreenshotsByIdResponses[keyof PatchApiScreenshotsByIdResponses];

export type GetApiProductsData = {
    body?: never;
    path?: never;
    query?: {
        page?: number;
        limit?: number;
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
        sort?: 'name' | '-name' | 'description' | '-description' | 'stripeID' | '-stripeID' | 'updatedAt' | '-updatedAt' | 'createdAt' | '-createdAt';
        where?: {
            [key: string]: unknown;
        } & (ProductQueryOperations | ProductQueryOperationsAnd | ProductQueryOperationsOr);
    };
    url: '/api/products';
};

export type GetApiProductsResponses = {
    /**
     * List of Products
     */
    200: {
        docs: Array<Product>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage: number | null;
        nextPage: number | null;
    };
};

export type GetApiProductsResponse = GetApiProductsResponses[keyof GetApiProductsResponses];

export type PostApiProductsData = {
    /**
     * Product
     */
    body?: ProductRequestBody;
    path?: never;
    query?: never;
    url: '/api/products';
};

export type PostApiProductsResponses = {
    /**
     * Product object
     */
    201: {
        message: string;
        doc: Product & {
            id: string;
            createdAt: string;
            updatedAt: string;
        };
    };
};

export type PostApiProductsResponse = PostApiProductsResponses[keyof PostApiProductsResponses];

export type DeleteApiProductsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Product
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/products/{id}';
};

export type DeleteApiProductsByIdErrors = {
    /**
     * Product not found
     */
    404: unknown;
};

export type DeleteApiProductsByIdResponses = {
    /**
     * Product object
     */
    200: Product;
};

export type DeleteApiProductsByIdResponse = DeleteApiProductsByIdResponses[keyof DeleteApiProductsByIdResponses];

export type GetApiProductsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Product
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/products/{id}';
};

export type GetApiProductsByIdErrors = {
    /**
     * Product not found
     */
    404: unknown;
};

export type GetApiProductsByIdResponses = {
    /**
     * Product object
     */
    200: Product;
};

export type GetApiProductsByIdResponse = GetApiProductsByIdResponses[keyof GetApiProductsByIdResponses];

export type PatchApiProductsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Product
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/products/{id}';
};

export type PatchApiProductsByIdErrors = {
    /**
     * Product not found
     */
    404: unknown;
};

export type PatchApiProductsByIdResponses = {
    /**
     * Product object
     */
    200: Product;
};

export type PatchApiProductsByIdResponse = PatchApiProductsByIdResponses[keyof PatchApiProductsByIdResponses];

export type GetApiSubscriptionsData = {
    body?: never;
    path?: never;
    query?: {
        page?: number;
        limit?: number;
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
        sort?: 'subscriptionId' | '-subscriptionId' | 'updatedAt' | '-updatedAt' | 'createdAt' | '-createdAt';
        where?: {
            [key: string]: unknown;
        } & (SubscriptionQueryOperations | SubscriptionQueryOperationsAnd | SubscriptionQueryOperationsOr);
    };
    url: '/api/subscriptions';
};

export type GetApiSubscriptionsResponses = {
    /**
     * List of Subscriptions
     */
    200: {
        docs: Array<Subscription>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage: number | null;
        nextPage: number | null;
    };
};

export type GetApiSubscriptionsResponse = GetApiSubscriptionsResponses[keyof GetApiSubscriptionsResponses];

export type PostApiSubscriptionsData = {
    /**
     * Subscription
     */
    body?: SubscriptionRequestBody;
    path?: never;
    query?: never;
    url: '/api/subscriptions';
};

export type PostApiSubscriptionsResponses = {
    /**
     * Subscription object
     */
    201: {
        message: string;
        doc: Subscription & {
            id: string;
            createdAt: string;
            updatedAt: string;
        };
    };
};

export type PostApiSubscriptionsResponse = PostApiSubscriptionsResponses[keyof PostApiSubscriptionsResponses];

export type DeleteApiSubscriptionsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Subscription
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/subscriptions/{id}';
};

export type DeleteApiSubscriptionsByIdErrors = {
    /**
     * Subscription not found
     */
    404: unknown;
};

export type DeleteApiSubscriptionsByIdResponses = {
    /**
     * Subscription object
     */
    200: Subscription;
};

export type DeleteApiSubscriptionsByIdResponse = DeleteApiSubscriptionsByIdResponses[keyof DeleteApiSubscriptionsByIdResponses];

export type GetApiSubscriptionsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Subscription
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/subscriptions/{id}';
};

export type GetApiSubscriptionsByIdErrors = {
    /**
     * Subscription not found
     */
    404: unknown;
};

export type GetApiSubscriptionsByIdResponses = {
    /**
     * Subscription object
     */
    200: Subscription;
};

export type GetApiSubscriptionsByIdResponse = GetApiSubscriptionsByIdResponses[keyof GetApiSubscriptionsByIdResponses];

export type PatchApiSubscriptionsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Subscription
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/subscriptions/{id}';
};

export type PatchApiSubscriptionsByIdErrors = {
    /**
     * Subscription not found
     */
    404: unknown;
};

export type PatchApiSubscriptionsByIdResponses = {
    /**
     * Subscription object
     */
    200: Subscription;
};

export type PatchApiSubscriptionsByIdResponse = PatchApiSubscriptionsByIdResponses[keyof PatchApiSubscriptionsByIdResponses];

export type GetApiSessionsData = {
    body?: never;
    path?: never;
    query?: {
        page?: number;
        limit?: number;
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
        sort?: 'id' | '-id' | 'familyId' | '-familyId' | 'userId' | '-userId' | 'token' | '-token' | 'expiresAt' | '-expiresAt' | 'ipAddress' | '-ipAddress' | 'userAgent' | '-userAgent' | 'updatedAt' | '-updatedAt' | 'createdAt' | '-createdAt';
        where?: {
            [key: string]: unknown;
        } & (SessionQueryOperations | SessionQueryOperationsAnd | SessionQueryOperationsOr);
    };
    url: '/api/sessions';
};

export type GetApiSessionsResponses = {
    /**
     * List of Sessions
     */
    200: {
        docs: Array<Session>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage: number | null;
        nextPage: number | null;
    };
};

export type GetApiSessionsResponse = GetApiSessionsResponses[keyof GetApiSessionsResponses];

export type PostApiSessionsData = {
    /**
     * Session
     */
    body?: SessionRequestBody;
    path?: never;
    query?: never;
    url: '/api/sessions';
};

export type PostApiSessionsResponses = {
    /**
     * Session object
     */
    201: {
        message: string;
        doc: Session & {
            id: string;
            createdAt: string;
            updatedAt: string;
        };
    };
};

export type PostApiSessionsResponse = PostApiSessionsResponses[keyof PostApiSessionsResponses];

export type DeleteApiSessionsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Session
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/sessions/{id}';
};

export type DeleteApiSessionsByIdErrors = {
    /**
     * Session not found
     */
    404: unknown;
};

export type DeleteApiSessionsByIdResponses = {
    /**
     * Session object
     */
    200: Session;
};

export type DeleteApiSessionsByIdResponse = DeleteApiSessionsByIdResponses[keyof DeleteApiSessionsByIdResponses];

export type GetApiSessionsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Session
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/sessions/{id}';
};

export type GetApiSessionsByIdErrors = {
    /**
     * Session not found
     */
    404: unknown;
};

export type GetApiSessionsByIdResponses = {
    /**
     * Session object
     */
    200: Session;
};

export type GetApiSessionsByIdResponse = GetApiSessionsByIdResponses[keyof GetApiSessionsByIdResponses];

export type PatchApiSessionsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Session
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/sessions/{id}';
};

export type PatchApiSessionsByIdErrors = {
    /**
     * Session not found
     */
    404: unknown;
};

export type PatchApiSessionsByIdResponses = {
    /**
     * Session object
     */
    200: Session;
};

export type PatchApiSessionsByIdResponse = PatchApiSessionsByIdResponses[keyof PatchApiSessionsByIdResponses];

export type GetApiRedirectsData = {
    body?: never;
    path?: never;
    query?: {
        page?: number;
        limit?: number;
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
        sort?: 'from' | '-from' | 'updatedAt' | '-updatedAt' | 'createdAt' | '-createdAt';
        where?: {
            [key: string]: unknown;
        } & (RedirectQueryOperations | RedirectQueryOperationsAnd | RedirectQueryOperationsOr);
    };
    url: '/api/redirects';
};

export type GetApiRedirectsResponses = {
    /**
     * List of Redirects
     */
    200: {
        docs: Array<Redirect>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage: number | null;
        nextPage: number | null;
    };
};

export type GetApiRedirectsResponse = GetApiRedirectsResponses[keyof GetApiRedirectsResponses];

export type PostApiRedirectsData = {
    /**
     * Redirect
     */
    body?: RedirectRequestBody;
    path?: never;
    query?: never;
    url: '/api/redirects';
};

export type PostApiRedirectsResponses = {
    /**
     * Redirect object
     */
    201: {
        message: string;
        doc: Redirect & {
            id: string;
            createdAt: string;
            updatedAt: string;
        };
    };
};

export type PostApiRedirectsResponse = PostApiRedirectsResponses[keyof PostApiRedirectsResponses];

export type DeleteApiRedirectsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Redirect
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/redirects/{id}';
};

export type DeleteApiRedirectsByIdErrors = {
    /**
     * Redirect not found
     */
    404: unknown;
};

export type DeleteApiRedirectsByIdResponses = {
    /**
     * Redirect object
     */
    200: Redirect;
};

export type DeleteApiRedirectsByIdResponse = DeleteApiRedirectsByIdResponses[keyof DeleteApiRedirectsByIdResponses];

export type GetApiRedirectsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Redirect
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/redirects/{id}';
};

export type GetApiRedirectsByIdErrors = {
    /**
     * Redirect not found
     */
    404: unknown;
};

export type GetApiRedirectsByIdResponses = {
    /**
     * Redirect object
     */
    200: Redirect;
};

export type GetApiRedirectsByIdResponse = GetApiRedirectsByIdResponses[keyof GetApiRedirectsByIdResponses];

export type PatchApiRedirectsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Redirect
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/redirects/{id}';
};

export type PatchApiRedirectsByIdErrors = {
    /**
     * Redirect not found
     */
    404: unknown;
};

export type PatchApiRedirectsByIdResponses = {
    /**
     * Redirect object
     */
    200: Redirect;
};

export type PatchApiRedirectsByIdResponse = PatchApiRedirectsByIdResponses[keyof PatchApiRedirectsByIdResponses];

export type GetApiFormsData = {
    body?: never;
    path?: never;
    query?: {
        page?: number;
        limit?: number;
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
        sort?: 'title' | '-title' | 'submitButtonLabel' | '-submitButtonLabel' | 'updatedAt' | '-updatedAt' | 'createdAt' | '-createdAt';
        where?: {
            [key: string]: unknown;
        } & (FormQueryOperations | FormQueryOperationsAnd | FormQueryOperationsOr);
    };
    url: '/api/forms';
};

export type GetApiFormsResponses = {
    /**
     * List of Forms
     */
    200: {
        docs: Array<Form>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage: number | null;
        nextPage: number | null;
    };
};

export type GetApiFormsResponse = GetApiFormsResponses[keyof GetApiFormsResponses];

export type PostApiFormsData = {
    /**
     * Form
     */
    body?: FormRequestBody;
    path?: never;
    query?: never;
    url: '/api/forms';
};

export type PostApiFormsResponses = {
    /**
     * Form object
     */
    201: {
        message: string;
        doc: Form & {
            id: string;
            createdAt: string;
            updatedAt: string;
        };
    };
};

export type PostApiFormsResponse = PostApiFormsResponses[keyof PostApiFormsResponses];

export type DeleteApiFormsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Form
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/forms/{id}';
};

export type DeleteApiFormsByIdErrors = {
    /**
     * Form not found
     */
    404: unknown;
};

export type DeleteApiFormsByIdResponses = {
    /**
     * Form object
     */
    200: Form;
};

export type DeleteApiFormsByIdResponse = DeleteApiFormsByIdResponses[keyof DeleteApiFormsByIdResponses];

export type GetApiFormsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Form
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/forms/{id}';
};

export type GetApiFormsByIdErrors = {
    /**
     * Form not found
     */
    404: unknown;
};

export type GetApiFormsByIdResponses = {
    /**
     * Form object
     */
    200: Form;
};

export type GetApiFormsByIdResponse = GetApiFormsByIdResponses[keyof GetApiFormsByIdResponses];

export type PatchApiFormsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Form
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/forms/{id}';
};

export type PatchApiFormsByIdErrors = {
    /**
     * Form not found
     */
    404: unknown;
};

export type PatchApiFormsByIdResponses = {
    /**
     * Form object
     */
    200: Form;
};

export type PatchApiFormsByIdResponse = PatchApiFormsByIdResponses[keyof PatchApiFormsByIdResponses];

export type GetApiFormSubmissionsData = {
    body?: never;
    path?: never;
    query?: {
        page?: number;
        limit?: number;
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
        sort?: 'updatedAt' | '-updatedAt' | 'createdAt' | '-createdAt';
        where?: {
            [key: string]: unknown;
        } & (FormSubmissionQueryOperations | FormSubmissionQueryOperationsAnd | FormSubmissionQueryOperationsOr);
    };
    url: '/api/form-submissions';
};

export type GetApiFormSubmissionsResponses = {
    /**
     * List of Form Submissions
     */
    200: {
        docs: Array<FormSubmission>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage: number | null;
        nextPage: number | null;
    };
};

export type GetApiFormSubmissionsResponse = GetApiFormSubmissionsResponses[keyof GetApiFormSubmissionsResponses];

export type PostApiFormSubmissionsData = {
    /**
     * Form Submission
     */
    body?: FormSubmissionRequestBody;
    path?: never;
    query?: never;
    url: '/api/form-submissions';
};

export type PostApiFormSubmissionsResponses = {
    /**
     * Form Submission object
     */
    201: {
        message: string;
        doc: FormSubmission & {
            id: string;
            createdAt: string;
            updatedAt: string;
        };
    };
};

export type PostApiFormSubmissionsResponse = PostApiFormSubmissionsResponses[keyof PostApiFormSubmissionsResponses];

export type DeleteApiFormSubmissionsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Form Submission
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/form-submissions/{id}';
};

export type DeleteApiFormSubmissionsByIdErrors = {
    /**
     * Form Submission not found
     */
    404: unknown;
};

export type DeleteApiFormSubmissionsByIdResponses = {
    /**
     * Form Submission object
     */
    200: FormSubmission;
};

export type DeleteApiFormSubmissionsByIdResponse = DeleteApiFormSubmissionsByIdResponses[keyof DeleteApiFormSubmissionsByIdResponses];

export type GetApiFormSubmissionsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Form Submission
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/form-submissions/{id}';
};

export type GetApiFormSubmissionsByIdErrors = {
    /**
     * Form Submission not found
     */
    404: unknown;
};

export type GetApiFormSubmissionsByIdResponses = {
    /**
     * Form Submission object
     */
    200: FormSubmission;
};

export type GetApiFormSubmissionsByIdResponse = GetApiFormSubmissionsByIdResponses[keyof GetApiFormSubmissionsByIdResponses];

export type PatchApiFormSubmissionsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Form Submission
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/form-submissions/{id}';
};

export type PatchApiFormSubmissionsByIdErrors = {
    /**
     * Form Submission not found
     */
    404: unknown;
};

export type PatchApiFormSubmissionsByIdResponses = {
    /**
     * Form Submission object
     */
    200: FormSubmission;
};

export type PatchApiFormSubmissionsByIdResponse = PatchApiFormSubmissionsByIdResponses[keyof PatchApiFormSubmissionsByIdResponses];

export type GetApiSearchData = {
    body?: never;
    path?: never;
    query?: {
        page?: number;
        limit?: number;
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
        sort?: 'title' | '-title' | 'priority' | '-priority' | 'slug' | '-slug' | 'updatedAt' | '-updatedAt' | 'createdAt' | '-createdAt';
        where?: {
            [key: string]: unknown;
        } & (SearchResultQueryOperations | SearchResultQueryOperationsAnd | SearchResultQueryOperationsOr);
    };
    url: '/api/search';
};

export type GetApiSearchResponses = {
    /**
     * List of Search Results
     */
    200: {
        docs: Array<SearchResult>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage: number | null;
        nextPage: number | null;
    };
};

export type GetApiSearchResponse = GetApiSearchResponses[keyof GetApiSearchResponses];

export type PostApiSearchData = {
    /**
     * Search Result
     */
    body?: SearchResultRequestBody;
    path?: never;
    query?: never;
    url: '/api/search';
};

export type PostApiSearchResponses = {
    /**
     * Search Result object
     */
    201: {
        message: string;
        doc: SearchResult & {
            id: string;
            createdAt: string;
            updatedAt: string;
        };
    };
};

export type PostApiSearchResponse = PostApiSearchResponses[keyof PostApiSearchResponses];

export type DeleteApiSearchByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Search Result
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/search/{id}';
};

export type DeleteApiSearchByIdErrors = {
    /**
     * Search Result not found
     */
    404: unknown;
};

export type DeleteApiSearchByIdResponses = {
    /**
     * Search Result object
     */
    200: SearchResult;
};

export type DeleteApiSearchByIdResponse = DeleteApiSearchByIdResponses[keyof DeleteApiSearchByIdResponses];

export type GetApiSearchByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Search Result
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/search/{id}';
};

export type GetApiSearchByIdErrors = {
    /**
     * Search Result not found
     */
    404: unknown;
};

export type GetApiSearchByIdResponses = {
    /**
     * Search Result object
     */
    200: SearchResult;
};

export type GetApiSearchByIdResponse = GetApiSearchByIdResponses[keyof GetApiSearchByIdResponses];

export type PatchApiSearchByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Search Result
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/search/{id}';
};

export type PatchApiSearchByIdErrors = {
    /**
     * Search Result not found
     */
    404: unknown;
};

export type PatchApiSearchByIdResponses = {
    /**
     * Search Result object
     */
    200: SearchResult;
};

export type PatchApiSearchByIdResponse = PatchApiSearchByIdResponses[keyof PatchApiSearchByIdResponses];

export type GetApiPayloadJobsData = {
    body?: never;
    path?: never;
    query?: {
        page?: number;
        limit?: number;
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
        sort?: 'queue' | '-queue' | 'waitUntil' | '-waitUntil' | 'updatedAt' | '-updatedAt' | 'createdAt' | '-createdAt';
        where?: {
            [key: string]: unknown;
        } & (PayloadJobQueryOperations | PayloadJobQueryOperationsAnd | PayloadJobQueryOperationsOr);
    };
    url: '/api/payload-jobs';
};

export type GetApiPayloadJobsResponses = {
    /**
     * List of Payload Jobs
     */
    200: {
        docs: Array<PayloadJob>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage: number | null;
        nextPage: number | null;
    };
};

export type GetApiPayloadJobsResponse = GetApiPayloadJobsResponses[keyof GetApiPayloadJobsResponses];

export type PostApiPayloadJobsData = {
    /**
     * Payload Job
     */
    body?: PayloadJobRequestBody;
    path?: never;
    query?: never;
    url: '/api/payload-jobs';
};

export type PostApiPayloadJobsResponses = {
    /**
     * Payload Job object
     */
    201: {
        message: string;
        doc: PayloadJob & {
            id: string;
            createdAt: string;
            updatedAt: string;
        };
    };
};

export type PostApiPayloadJobsResponse = PostApiPayloadJobsResponses[keyof PostApiPayloadJobsResponses];

export type DeleteApiPayloadJobsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Payload Job
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/payload-jobs/{id}';
};

export type DeleteApiPayloadJobsByIdErrors = {
    /**
     * Payload Job not found
     */
    404: unknown;
};

export type DeleteApiPayloadJobsByIdResponses = {
    /**
     * Payload Job object
     */
    200: PayloadJob;
};

export type DeleteApiPayloadJobsByIdResponse = DeleteApiPayloadJobsByIdResponses[keyof DeleteApiPayloadJobsByIdResponses];

export type GetApiPayloadJobsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Payload Job
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/payload-jobs/{id}';
};

export type GetApiPayloadJobsByIdErrors = {
    /**
     * Payload Job not found
     */
    404: unknown;
};

export type GetApiPayloadJobsByIdResponses = {
    /**
     * Payload Job object
     */
    200: PayloadJob;
};

export type GetApiPayloadJobsByIdResponse = GetApiPayloadJobsByIdResponses[keyof GetApiPayloadJobsByIdResponses];

export type PatchApiPayloadJobsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Payload Job
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/payload-jobs/{id}';
};

export type PatchApiPayloadJobsByIdErrors = {
    /**
     * Payload Job not found
     */
    404: unknown;
};

export type PatchApiPayloadJobsByIdResponses = {
    /**
     * Payload Job object
     */
    200: PayloadJob;
};

export type PatchApiPayloadJobsByIdResponse = PatchApiPayloadJobsByIdResponses[keyof PatchApiPayloadJobsByIdResponses];

export type GetApiPayloadLockedDocumentsData = {
    body?: never;
    path?: never;
    query?: {
        page?: number;
        limit?: number;
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
        sort?: 'globalSlug' | '-globalSlug' | 'updatedAt' | '-updatedAt' | 'createdAt' | '-createdAt';
        where?: {
            [key: string]: unknown;
        } & (PayloadLockedDocumentQueryOperations | PayloadLockedDocumentQueryOperationsAnd | PayloadLockedDocumentQueryOperationsOr);
    };
    url: '/api/payload-locked-documents';
};

export type GetApiPayloadLockedDocumentsResponses = {
    /**
     * List of Payload Locked Documents
     */
    200: {
        docs: Array<PayloadLockedDocument>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage: number | null;
        nextPage: number | null;
    };
};

export type GetApiPayloadLockedDocumentsResponse = GetApiPayloadLockedDocumentsResponses[keyof GetApiPayloadLockedDocumentsResponses];

export type PostApiPayloadLockedDocumentsData = {
    /**
     * Payload Locked Document
     */
    body?: PayloadLockedDocumentRequestBody;
    path?: never;
    query?: never;
    url: '/api/payload-locked-documents';
};

export type PostApiPayloadLockedDocumentsResponses = {
    /**
     * Payload Locked Document object
     */
    201: {
        message: string;
        doc: PayloadLockedDocument & {
            id: string;
            createdAt: string;
            updatedAt: string;
        };
    };
};

export type PostApiPayloadLockedDocumentsResponse = PostApiPayloadLockedDocumentsResponses[keyof PostApiPayloadLockedDocumentsResponses];

export type DeleteApiPayloadLockedDocumentsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Payload Locked Document
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/payload-locked-documents/{id}';
};

export type DeleteApiPayloadLockedDocumentsByIdErrors = {
    /**
     * Payload Locked Document not found
     */
    404: unknown;
};

export type DeleteApiPayloadLockedDocumentsByIdResponses = {
    /**
     * Payload Locked Document object
     */
    200: PayloadLockedDocument;
};

export type DeleteApiPayloadLockedDocumentsByIdResponse = DeleteApiPayloadLockedDocumentsByIdResponses[keyof DeleteApiPayloadLockedDocumentsByIdResponses];

export type GetApiPayloadLockedDocumentsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Payload Locked Document
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/payload-locked-documents/{id}';
};

export type GetApiPayloadLockedDocumentsByIdErrors = {
    /**
     * Payload Locked Document not found
     */
    404: unknown;
};

export type GetApiPayloadLockedDocumentsByIdResponses = {
    /**
     * Payload Locked Document object
     */
    200: PayloadLockedDocument;
};

export type GetApiPayloadLockedDocumentsByIdResponse = GetApiPayloadLockedDocumentsByIdResponses[keyof GetApiPayloadLockedDocumentsByIdResponses];

export type PatchApiPayloadLockedDocumentsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Payload Locked Document
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/payload-locked-documents/{id}';
};

export type PatchApiPayloadLockedDocumentsByIdErrors = {
    /**
     * Payload Locked Document not found
     */
    404: unknown;
};

export type PatchApiPayloadLockedDocumentsByIdResponses = {
    /**
     * Payload Locked Document object
     */
    200: PayloadLockedDocument;
};

export type PatchApiPayloadLockedDocumentsByIdResponse = PatchApiPayloadLockedDocumentsByIdResponses[keyof PatchApiPayloadLockedDocumentsByIdResponses];

export type GetApiPayloadPreferencesData = {
    body?: never;
    path?: never;
    query?: {
        page?: number;
        limit?: number;
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
        sort?: 'key' | '-key' | 'updatedAt' | '-updatedAt' | 'createdAt' | '-createdAt';
        where?: {
            [key: string]: unknown;
        } & (PayloadPreferenceQueryOperations | PayloadPreferenceQueryOperationsAnd | PayloadPreferenceQueryOperationsOr);
    };
    url: '/api/payload-preferences';
};

export type GetApiPayloadPreferencesResponses = {
    /**
     * List of Payload Preferences
     */
    200: {
        docs: Array<PayloadPreference>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage: number | null;
        nextPage: number | null;
    };
};

export type GetApiPayloadPreferencesResponse = GetApiPayloadPreferencesResponses[keyof GetApiPayloadPreferencesResponses];

export type PostApiPayloadPreferencesData = {
    /**
     * Payload Preference
     */
    body?: PayloadPreferenceRequestBody;
    path?: never;
    query?: never;
    url: '/api/payload-preferences';
};

export type PostApiPayloadPreferencesResponses = {
    /**
     * Payload Preference object
     */
    201: {
        message: string;
        doc: PayloadPreference & {
            id: string;
            createdAt: string;
            updatedAt: string;
        };
    };
};

export type PostApiPayloadPreferencesResponse = PostApiPayloadPreferencesResponses[keyof PostApiPayloadPreferencesResponses];

export type DeleteApiPayloadPreferencesByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Payload Preference
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/payload-preferences/{id}';
};

export type DeleteApiPayloadPreferencesByIdErrors = {
    /**
     * Payload Preference not found
     */
    404: unknown;
};

export type DeleteApiPayloadPreferencesByIdResponses = {
    /**
     * Payload Preference object
     */
    200: PayloadPreference;
};

export type DeleteApiPayloadPreferencesByIdResponse = DeleteApiPayloadPreferencesByIdResponses[keyof DeleteApiPayloadPreferencesByIdResponses];

export type GetApiPayloadPreferencesByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Payload Preference
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/payload-preferences/{id}';
};

export type GetApiPayloadPreferencesByIdErrors = {
    /**
     * Payload Preference not found
     */
    404: unknown;
};

export type GetApiPayloadPreferencesByIdResponses = {
    /**
     * Payload Preference object
     */
    200: PayloadPreference;
};

export type GetApiPayloadPreferencesByIdResponse = GetApiPayloadPreferencesByIdResponses[keyof GetApiPayloadPreferencesByIdResponses];

export type PatchApiPayloadPreferencesByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Payload Preference
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/payload-preferences/{id}';
};

export type PatchApiPayloadPreferencesByIdErrors = {
    /**
     * Payload Preference not found
     */
    404: unknown;
};

export type PatchApiPayloadPreferencesByIdResponses = {
    /**
     * Payload Preference object
     */
    200: PayloadPreference;
};

export type PatchApiPayloadPreferencesByIdResponse = PatchApiPayloadPreferencesByIdResponses[keyof PatchApiPayloadPreferencesByIdResponses];

export type GetApiPayloadMigrationsData = {
    body?: never;
    path?: never;
    query?: {
        page?: number;
        limit?: number;
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
        sort?: 'name' | '-name' | 'batch' | '-batch' | 'updatedAt' | '-updatedAt' | 'createdAt' | '-createdAt';
        where?: {
            [key: string]: unknown;
        } & (PayloadMigrationQueryOperations | PayloadMigrationQueryOperationsAnd | PayloadMigrationQueryOperationsOr);
    };
    url: '/api/payload-migrations';
};

export type GetApiPayloadMigrationsResponses = {
    /**
     * List of Payload Migrations
     */
    200: {
        docs: Array<PayloadMigration>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage: number | null;
        nextPage: number | null;
    };
};

export type GetApiPayloadMigrationsResponse = GetApiPayloadMigrationsResponses[keyof GetApiPayloadMigrationsResponses];

export type PostApiPayloadMigrationsData = {
    /**
     * Payload Migration
     */
    body?: PayloadMigrationRequestBody;
    path?: never;
    query?: never;
    url: '/api/payload-migrations';
};

export type PostApiPayloadMigrationsResponses = {
    /**
     * Payload Migration object
     */
    201: {
        message: string;
        doc: PayloadMigration & {
            id: string;
            createdAt: string;
            updatedAt: string;
        };
    };
};

export type PostApiPayloadMigrationsResponse = PostApiPayloadMigrationsResponses[keyof PostApiPayloadMigrationsResponses];

export type DeleteApiPayloadMigrationsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Payload Migration
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/payload-migrations/{id}';
};

export type DeleteApiPayloadMigrationsByIdErrors = {
    /**
     * Payload Migration not found
     */
    404: unknown;
};

export type DeleteApiPayloadMigrationsByIdResponses = {
    /**
     * Payload Migration object
     */
    200: PayloadMigration;
};

export type DeleteApiPayloadMigrationsByIdResponse = DeleteApiPayloadMigrationsByIdResponses[keyof DeleteApiPayloadMigrationsByIdResponses];

export type GetApiPayloadMigrationsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Payload Migration
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/payload-migrations/{id}';
};

export type GetApiPayloadMigrationsByIdErrors = {
    /**
     * Payload Migration not found
     */
    404: unknown;
};

export type GetApiPayloadMigrationsByIdResponses = {
    /**
     * Payload Migration object
     */
    200: PayloadMigration;
};

export type GetApiPayloadMigrationsByIdResponse = GetApiPayloadMigrationsByIdResponses[keyof GetApiPayloadMigrationsByIdResponses];

export type PatchApiPayloadMigrationsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Payload Migration
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/payload-migrations/{id}';
};

export type PatchApiPayloadMigrationsByIdErrors = {
    /**
     * Payload Migration not found
     */
    404: unknown;
};

export type PatchApiPayloadMigrationsByIdResponses = {
    /**
     * Payload Migration object
     */
    200: PayloadMigration;
};

export type PatchApiPayloadMigrationsByIdResponse = PatchApiPayloadMigrationsByIdResponses[keyof PatchApiPayloadMigrationsByIdResponses];

export type PostApiAdminsLoginData = {
    /**
     * Admin login request
     */
    body?: AdminLoginRequestBody;
    path?: never;
    query?: never;
    url: '/api/admins/login';
};

export type PostApiAdminsLoginErrors = {
    /**
     * Invalid credentials
     */
    401: unknown;
};

export type PostApiAdminsLoginResponses = {
    /**
     * Admin login response
     */
    200: AdminLoginResponse;
};

export type PostApiAdminsLoginResponse = PostApiAdminsLoginResponses[keyof PostApiAdminsLoginResponses];

export type PostApiAdminsLogoutData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/admins/logout';
};

export type PostApiAdminsLogoutResponses = {
    /**
     * Admin logout response
     */
    200: AdminLogoutResponse;
};

export type PostApiAdminsLogoutResponse = PostApiAdminsLogoutResponses[keyof PostApiAdminsLogoutResponses];

export type PostApiAdminsRefreshTokenData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/admins/refresh-token';
};

export type PostApiAdminsRefreshTokenErrors = {
    /**
     * Invalid or expired token
     */
    401: unknown;
};

export type PostApiAdminsRefreshTokenResponses = {
    /**
     * Admin refreshToken response
     */
    200: AdminRefreshTokenResponse;
};

export type PostApiAdminsRefreshTokenResponse = PostApiAdminsRefreshTokenResponses[keyof PostApiAdminsRefreshTokenResponses];

export type GetApiAdminsMeData = {
    body?: never;
    path?: never;
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/admins/me';
};

export type GetApiAdminsMeErrors = {
    /**
     * Not authenticated
     */
    401: unknown;
};

export type GetApiAdminsMeResponses = {
    /**
     * Admin current user response
     */
    200: AdminMeResponse;
};

export type GetApiAdminsMeResponse = GetApiAdminsMeResponses[keyof GetApiAdminsMeResponses];

export type PostApiAdminsForgotPasswordData = {
    /**
     * Admin forgot password request
     */
    body?: AdminForgotPasswordRequestBody;
    path?: never;
    query?: never;
    url: '/api/admins/forgot-password';
};

export type PostApiAdminsForgotPasswordErrors = {
    /**
     * User not found
     */
    404: unknown;
};

export type PostApiAdminsForgotPasswordResponses = {
    /**
     * Admin forgot password response
     */
    200: AdminForgotPasswordResponse;
};

export type PostApiAdminsForgotPasswordResponse = PostApiAdminsForgotPasswordResponses[keyof PostApiAdminsForgotPasswordResponses];

export type PostApiAdminsResetPasswordData = {
    /**
     * Admin reset password request
     */
    body?: AdminResetPasswordRequestBody;
    path?: never;
    query?: never;
    url: '/api/admins/reset-password';
};

export type PostApiAdminsResetPasswordErrors = {
    /**
     * Invalid or expired token
     */
    400: unknown;
};

export type PostApiAdminsResetPasswordResponses = {
    /**
     * Admin reset password response
     */
    200: AdminResetPasswordResponse;
};

export type PostApiAdminsResetPasswordResponse = PostApiAdminsResetPasswordResponses[keyof PostApiAdminsResetPasswordResponses];

export type PostApiAdminsVerifyByTokenData = {
    body?: never;
    path: {
        /**
         * Verification token
         */
        token: string;
    };
    query?: never;
    url: '/api/admins/verify/{token}';
};

export type PostApiAdminsVerifyByTokenErrors = {
    /**
     * Invalid or expired token
     */
    400: unknown;
};

export type PostApiAdminsVerifyByTokenResponses = {
    /**
     * Admin verify response
     */
    200: AdminVerifyResponse;
};

export type PostApiAdminsVerifyByTokenResponse = PostApiAdminsVerifyByTokenResponses[keyof PostApiAdminsVerifyByTokenResponses];

export type PostApiAdminsUnlockData = {
    /**
     * Admin unlock request
     */
    body?: AdminUnlockRequestBody;
    path?: never;
    query?: never;
    url: '/api/admins/unlock';
};

export type PostApiAdminsUnlockErrors = {
    /**
     * Invalid credentials or account not locked
     */
    400: unknown;
};

export type PostApiAdminsUnlockResponses = {
    /**
     * Admin verify response
     */
    200: AdminVerifyResponse;
};

export type PostApiAdminsUnlockResponse = PostApiAdminsUnlockResponses[keyof PostApiAdminsUnlockResponses];

export type PostApiUsersOtpRequestData = {
    /**
     * Send OTP to user email for authentication
     */
    body?: PayloadMigrationOtpRequestRequestBody;
    path?: never;
    query?: never;
    url: '/api/users/otp/request';
};

export type PostApiUsersOtpRequestErrors = {
    /**
     * Invalid email format or missing email
     */
    400: unknown;
    /**
     * Failed to send OTP email
     */
    500: unknown;
};

export type PostApiUsersOtpRequestResponses = {
    /**
     * Payload Migration otpRequest response
     */
    200: PayloadMigrationOtpRequestResponse;
};

export type PostApiUsersOtpRequestResponse = PostApiUsersOtpRequestResponses[keyof PostApiUsersOtpRequestResponses];

export type PostApiUsersOtpLoginData = {
    /**
     * Authenticate user using email and OTP
     */
    body?: PayloadMigrationOtpLoginRequestBody;
    path?: never;
    query?: never;
    url: '/api/users/otp/login';
};

export type PostApiUsersOtpLoginErrors = {
    /**
     * Invalid OTP or expired
     */
    401: unknown;
    /**
     * User not found
     */
    404: unknown;
};

export type PostApiUsersOtpLoginResponses = {
    /**
     * Payload Migration otpLogin response
     */
    200: PayloadMigrationOtpLoginResponse;
};

export type PostApiUsersOtpLoginResponse = PostApiUsersOtpLoginResponses[keyof PostApiUsersOtpLoginResponses];

export type PostApiUsersRefreshTokenData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/users/refresh-token';
};

export type PostApiUsersRefreshTokenErrors = {
    /**
     * Invalid or expired refresh token
     */
    401: unknown;
};

export type PostApiUsersRefreshTokenResponses = {
    /**
     * Payload Migration refreshToken response
     */
    200: PayloadMigrationRefreshTokenResponse;
};

export type PostApiUsersRefreshTokenResponse = PostApiUsersRefreshTokenResponses[keyof PostApiUsersRefreshTokenResponses];

export type GetApiGlobalsHeaderData = {
    body?: never;
    path?: never;
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/globals/header';
};

export type GetApiGlobalsHeaderResponses = {
    /**
     * Header
     */
    200: HeaderRead;
};

export type GetApiGlobalsHeaderResponse = GetApiGlobalsHeaderResponses[keyof GetApiGlobalsHeaderResponses];

export type PostApiGlobalsHeaderData = {
    /**
     * Header
     */
    body?: HeaderRequestBody;
    path?: never;
    query?: never;
    url: '/api/globals/header';
};

export type PostApiGlobalsHeaderResponses = {
    /**
     * Header
     */
    200: HeaderRead;
};

export type PostApiGlobalsHeaderResponse = PostApiGlobalsHeaderResponses[keyof PostApiGlobalsHeaderResponses];

export type GetApiGlobalsFooterData = {
    body?: never;
    path?: never;
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/globals/footer';
};

export type GetApiGlobalsFooterResponses = {
    /**
     * Footer
     */
    200: FooterRead;
};

export type GetApiGlobalsFooterResponse = GetApiGlobalsFooterResponses[keyof GetApiGlobalsFooterResponses];

export type PostApiGlobalsFooterData = {
    /**
     * Footer
     */
    body?: FooterRequestBody;
    path?: never;
    query?: never;
    url: '/api/globals/footer';
};

export type PostApiGlobalsFooterResponses = {
    /**
     * Footer
     */
    200: FooterRead;
};

export type PostApiGlobalsFooterResponse = PostApiGlobalsFooterResponses[keyof PostApiGlobalsFooterResponses];

export type ClientOptions = {
    baseUrl: `${string}://${string}` | (string & {});
};