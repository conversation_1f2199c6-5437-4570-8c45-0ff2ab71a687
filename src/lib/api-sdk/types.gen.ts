// This file is auto-generated by @hey-api/openapi-ts

export type SupportedTimezones = string;

/**
 * Generic Object
 * Reference to a filtered collection or external resource
 */
export type GenericObject = {
    id?: string;
    [key: string]: unknown | string | undefined;
};

/**
 * Media
 */
export type Media = {
    id: string;
    alt?: string | null;
    caption?: {
        root: {
            type: string;
            children: Array<{
                type: string;
                version: number;
                [key: string]: unknown | string | number;
            }>;
            direction: 'ltr' | 'rtl';
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
        };
    } | null;
    prefix?: string | null;
    updatedAt: string;
    createdAt: string;
    url?: string | null;
    thumbnailURL?: string | null;
    filename?: string | null;
    mimeType?: string | null;
    filesize?: number | null;
    width?: number | null;
    height?: number | null;
    focalX?: number | null;
    focalY?: number | null;
    sizes?: {
        thumbnail?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
        square?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
        small?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
        medium?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
        large?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
        xlarge?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
        og?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
    };
};

/**
 * User
 */
export type User = {
    id: string;
    email: string;
    firstName?: string | null;
    lastName?: string | null;
    /**
     * Upload a profile image for the user
     */
    profileImage?: string | null | Media;
    /**
     * Social accounts for the user
     */
    socialAccounts?: Array<{
        provider: string;
        providerId: string;
        isActive?: boolean | null;
        linkedAt?: string | null;
        id?: string | null;
    }> | null;
    teams?: Array<{
        team: string | Team;
        roles: Array<'teamadmin' | 'teamviewer'>;
        id?: string | null;
    }> | null;
    _otp?: string | null;
    _otpExpiration?: string | null;
    updatedAt: string;
    createdAt: string;
};

/**
 * Team
 */
export type Team = {
    id: string;
    name: string;
    image?: string | null | Media;
    user: string | User;
    updatedAt: string;
    createdAt: string;
};

/**
 * Media query operations
 */
export type MediaQueryOperations = {
    alt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    prefix?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    updatedAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    createdAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    url?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    thumbnailURL?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    filename?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    mimeType?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    filesize?: {
        equals?: number;
        not_equals?: number;
        in?: string;
        not_in?: string;
        greater_than?: number;
        greater_than_equal?: number;
        less_than?: number;
        less_than_equal?: number;
    };
    width?: {
        equals?: number;
        not_equals?: number;
        in?: string;
        not_in?: string;
        greater_than?: number;
        greater_than_equal?: number;
        less_than?: number;
        less_than_equal?: number;
    };
    height?: {
        equals?: number;
        not_equals?: number;
        in?: string;
        not_in?: string;
        greater_than?: number;
        greater_than_equal?: number;
        less_than?: number;
        less_than_equal?: number;
    };
    focalX?: {
        equals?: number;
        not_equals?: number;
        in?: string;
        not_in?: string;
        greater_than?: number;
        greater_than_equal?: number;
        less_than?: number;
        less_than_equal?: number;
    };
    focalY?: {
        equals?: number;
        not_equals?: number;
        in?: string;
        not_in?: string;
        greater_than?: number;
        greater_than_equal?: number;
        less_than?: number;
        less_than_equal?: number;
    };
};

/**
 * Media query conjunction
 */
export type MediaQueryOperationsAnd = {
    and: Array<MediaQueryOperations | MediaQueryOperationsAnd | MediaQueryOperationsOr>;
};

/**
 * Media query disjunction
 */
export type MediaQueryOperationsOr = {
    or: Array<MediaQueryOperations | MediaQueryOperationsAnd | MediaQueryOperationsOr>;
};

/**
 * User query operations
 */
export type UserQueryOperations = {
    id?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    email?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        contains?: string;
    };
    firstName?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    lastName?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    _otp?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    _otpExpiration?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    updatedAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    createdAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
};

/**
 * User query conjunction
 */
export type UserQueryOperationsAnd = {
    and: Array<UserQueryOperations | UserQueryOperationsAnd | UserQueryOperationsOr>;
};

/**
 * User query disjunction
 */
export type UserQueryOperationsOr = {
    or: Array<UserQueryOperations | UserQueryOperationsAnd | UserQueryOperationsOr>;
};

/**
 * Team query operations
 */
export type TeamQueryOperations = {
    name?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        like?: string;
        contains?: string;
    };
    updatedAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
    createdAt?: {
        equals?: string;
        not_equals?: string;
        in?: string;
        not_in?: string;
        greater_than?: string;
        greater_than_equal?: string;
        less_than?: string;
        less_than_equal?: string;
    };
};

/**
 * Team query conjunction
 */
export type TeamQueryOperationsAnd = {
    and: Array<TeamQueryOperations | TeamQueryOperationsAnd | TeamQueryOperationsOr>;
};

/**
 * Team query disjunction
 */
export type TeamQueryOperationsOr = {
    or: Array<TeamQueryOperations | TeamQueryOperationsAnd | TeamQueryOperationsOr>;
};

/**
 * User OTP request
 */
export type UserOtpRequestRequest = {
    email: string;
};

/**
 * User OTP login request
 */
export type UserOtpLoginRequest = {
    email: string;
    otp: string;
};

/**
 * User OTP request response
 */
export type UserOtpRequestResponse = {
    type: string;
    message: string;
    value: string;
};

/**
 * User OTP login response
 */
export type UserOtpLoginResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * User refresh token response
 */
export type UserRefreshTokenResponse = {
    user: User;
    token: string;
    exp: number;
};

/**
 * Media
 * Media
 */
export type MediaRequestBody = {
    alt?: string | null;
    caption?: {
        root: {
            type: string;
            children: Array<{
                type: string;
                version: number;
                [key: string]: unknown | string | number;
            }>;
            direction: 'ltr' | 'rtl';
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
        };
    } | null;
    prefix?: string | null;
    url?: string | null;
    thumbnailURL?: string | null;
    filename?: string | null;
    mimeType?: string | null;
    filesize?: number | null;
    width?: number | null;
    height?: number | null;
    focalX?: number | null;
    focalY?: number | null;
    sizes?: {
        thumbnail?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
        square?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
        small?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
        medium?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
        large?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
        xlarge?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
        og?: {
            url?: string | null;
            width?: number | null;
            height?: number | null;
            mimeType?: string | null;
            filesize?: number | null;
            filename?: string | null;
        };
    };
};

/**
 * User
 * User
 */
export type UserRequestBody = {
    email: string;
    firstName?: string | null;
    lastName?: string | null;
    /**
     * Upload a profile image for the user
     */
    profileImage?: string | null | Media;
    /**
     * Social accounts for the user
     */
    socialAccounts?: Array<{
        provider: string;
        providerId: string;
        isActive?: boolean | null;
        linkedAt?: string | null;
        id?: string | null;
    }> | null;
    teams?: Array<{
        team: string | Team;
        roles: Array<'teamadmin' | 'teamviewer'>;
        id?: string | null;
    }> | null;
    _otp?: string | null;
    _otpExpiration?: string | null;
};

/**
 * Team
 * Team
 */
export type TeamRequestBody = {
    name: string;
    image?: string | null | Media;
    /**
     * ID of the users
     */
    user: string;
};

export type UserOtpRequestRequestBody = UserOtpRequestRequest;

export type UserOtpLoginRequestBody = UserOtpLoginRequest;

export type GetApiMediaData = {
    body?: never;
    path?: never;
    query?: {
        page?: number;
        limit?: number;
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
        sort?: 'alt' | '-alt' | 'prefix' | '-prefix' | 'updatedAt' | '-updatedAt' | 'createdAt' | '-createdAt' | 'url' | '-url' | 'thumbnailURL' | '-thumbnailURL' | 'filename' | '-filename' | 'mimeType' | '-mimeType' | 'filesize' | '-filesize' | 'width' | '-width' | 'height' | '-height' | 'focalX' | '-focalX' | 'focalY' | '-focalY';
        where?: {
            [key: string]: unknown;
        } & (MediaQueryOperations | MediaQueryOperationsAnd | MediaQueryOperationsOr);
    };
    url: '/api/media';
};

export type GetApiMediaResponses = {
    /**
     * List of Media
     */
    200: {
        docs: Array<Media>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage: number | null;
        nextPage: number | null;
    };
};

export type GetApiMediaResponse = GetApiMediaResponses[keyof GetApiMediaResponses];

export type PostApiMediaData = {
    /**
     * Media
     */
    body?: MediaRequestBody;
    path?: never;
    query?: never;
    url: '/api/media';
};

export type PostApiMediaResponses = {
    /**
     * Media object
     */
    201: {
        message: string;
        doc: Media & {
            id: string;
            createdAt: string;
            updatedAt: string;
        };
    };
};

export type PostApiMediaResponse = PostApiMediaResponses[keyof PostApiMediaResponses];

export type DeleteApiMediaByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Media
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/media/{id}';
};

export type DeleteApiMediaByIdErrors = {
    /**
     * Media not found
     */
    404: unknown;
};

export type DeleteApiMediaByIdResponses = {
    /**
     * Media object
     */
    200: Media;
};

export type DeleteApiMediaByIdResponse = DeleteApiMediaByIdResponses[keyof DeleteApiMediaByIdResponses];

export type GetApiMediaByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Media
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/media/{id}';
};

export type GetApiMediaByIdErrors = {
    /**
     * Media not found
     */
    404: unknown;
};

export type GetApiMediaByIdResponses = {
    /**
     * Media object
     */
    200: Media;
};

export type GetApiMediaByIdResponse = GetApiMediaByIdResponses[keyof GetApiMediaByIdResponses];

export type PatchApiMediaByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Media
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/media/{id}';
};

export type PatchApiMediaByIdErrors = {
    /**
     * Media not found
     */
    404: unknown;
};

export type PatchApiMediaByIdResponses = {
    /**
     * Media object
     */
    200: Media;
};

export type PatchApiMediaByIdResponse = PatchApiMediaByIdResponses[keyof PatchApiMediaByIdResponses];

export type GetApiUsersData = {
    body?: never;
    path?: never;
    query?: {
        page?: number;
        limit?: number;
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
        sort?: 'id' | '-id' | 'email' | '-email' | 'firstName' | '-firstName' | 'lastName' | '-lastName' | '_otp' | '-_otp' | '_otpExpiration' | '-_otpExpiration' | 'updatedAt' | '-updatedAt' | 'createdAt' | '-createdAt';
        where?: {
            [key: string]: unknown;
        } & (UserQueryOperations | UserQueryOperationsAnd | UserQueryOperationsOr);
    };
    url: '/api/users';
};

export type GetApiUsersResponses = {
    /**
     * List of Users
     */
    200: {
        docs: Array<User>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage: number | null;
        nextPage: number | null;
    };
};

export type GetApiUsersResponse = GetApiUsersResponses[keyof GetApiUsersResponses];

export type PostApiUsersData = {
    /**
     * User
     */
    body?: UserRequestBody;
    path?: never;
    query?: never;
    url: '/api/users';
};

export type PostApiUsersResponses = {
    /**
     * User object
     */
    201: {
        message: string;
        doc: User & {
            id: string;
            createdAt: string;
            updatedAt: string;
        };
    };
};

export type PostApiUsersResponse = PostApiUsersResponses[keyof PostApiUsersResponses];

export type DeleteApiUsersByIdData = {
    body?: never;
    path: {
        /**
         * ID of the User
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/users/{id}';
};

export type DeleteApiUsersByIdErrors = {
    /**
     * User not found
     */
    404: unknown;
};

export type DeleteApiUsersByIdResponses = {
    /**
     * User object
     */
    200: User;
};

export type DeleteApiUsersByIdResponse = DeleteApiUsersByIdResponses[keyof DeleteApiUsersByIdResponses];

export type GetApiUsersByIdData = {
    body?: never;
    path: {
        /**
         * ID of the User
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/users/{id}';
};

export type GetApiUsersByIdErrors = {
    /**
     * User not found
     */
    404: unknown;
};

export type GetApiUsersByIdResponses = {
    /**
     * User object
     */
    200: User;
};

export type GetApiUsersByIdResponse = GetApiUsersByIdResponses[keyof GetApiUsersByIdResponses];

export type PatchApiUsersByIdData = {
    body?: never;
    path: {
        /**
         * ID of the User
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/users/{id}';
};

export type PatchApiUsersByIdErrors = {
    /**
     * User not found
     */
    404: unknown;
};

export type PatchApiUsersByIdResponses = {
    /**
     * User object
     */
    200: User;
};

export type PatchApiUsersByIdResponse = PatchApiUsersByIdResponses[keyof PatchApiUsersByIdResponses];

export type GetApiTeamsData = {
    body?: never;
    path?: never;
    query?: {
        page?: number;
        limit?: number;
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
        sort?: 'name' | '-name' | 'updatedAt' | '-updatedAt' | 'createdAt' | '-createdAt';
        where?: {
            [key: string]: unknown;
        } & (TeamQueryOperations | TeamQueryOperationsAnd | TeamQueryOperationsOr);
    };
    url: '/api/teams';
};

export type GetApiTeamsResponses = {
    /**
     * List of Teams
     */
    200: {
        docs: Array<Team>;
        totalDocs: number;
        limit: number;
        totalPages: number;
        page: number;
        pagingCounter: number;
        hasPrevPage: boolean;
        hasNextPage: boolean;
        prevPage: number | null;
        nextPage: number | null;
    };
};

export type GetApiTeamsResponse = GetApiTeamsResponses[keyof GetApiTeamsResponses];

export type PostApiTeamsData = {
    /**
     * Team
     */
    body?: TeamRequestBody;
    path?: never;
    query?: never;
    url: '/api/teams';
};

export type PostApiTeamsResponses = {
    /**
     * Team object
     */
    201: {
        message: string;
        doc: Team & {
            id: string;
            createdAt: string;
            updatedAt: string;
        };
    };
};

export type PostApiTeamsResponse = PostApiTeamsResponses[keyof PostApiTeamsResponses];

export type DeleteApiTeamsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Team
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/teams/{id}';
};

export type DeleteApiTeamsByIdErrors = {
    /**
     * Team not found
     */
    404: unknown;
};

export type DeleteApiTeamsByIdResponses = {
    /**
     * Team object
     */
    200: Team;
};

export type DeleteApiTeamsByIdResponse = DeleteApiTeamsByIdResponses[keyof DeleteApiTeamsByIdResponses];

export type GetApiTeamsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Team
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/teams/{id}';
};

export type GetApiTeamsByIdErrors = {
    /**
     * Team not found
     */
    404: unknown;
};

export type GetApiTeamsByIdResponses = {
    /**
     * Team object
     */
    200: Team;
};

export type GetApiTeamsByIdResponse = GetApiTeamsByIdResponses[keyof GetApiTeamsByIdResponses];

export type PatchApiTeamsByIdData = {
    body?: never;
    path: {
        /**
         * ID of the Team
         */
        id: string;
    };
    query?: {
        depth?: number;
        locale?: string;
        'fallback-locale'?: string;
    };
    url: '/api/teams/{id}';
};

export type PatchApiTeamsByIdErrors = {
    /**
     * Team not found
     */
    404: unknown;
};

export type PatchApiTeamsByIdResponses = {
    /**
     * Team object
     */
    200: Team;
};

export type PatchApiTeamsByIdResponse = PatchApiTeamsByIdResponses[keyof PatchApiTeamsByIdResponses];

export type PostApiUsersOtpRequestData = {
    /**
     * Send OTP to user email for authentication
     */
    body?: UserOtpRequestRequestBody;
    path?: never;
    query?: never;
    url: '/api/users/otp/request';
};

export type PostApiUsersOtpRequestErrors = {
    /**
     * Invalid email format or missing email
     */
    400: unknown;
    /**
     * Failed to send OTP email
     */
    500: unknown;
};

export type PostApiUsersOtpRequestResponses = {
    /**
     * User otpRequest response
     */
    201: UserOtpRequestResponse;
};

export type PostApiUsersOtpRequestResponse = PostApiUsersOtpRequestResponses[keyof PostApiUsersOtpRequestResponses];

export type PostApiUsersOtpLoginData = {
    /**
     * Authenticate user using email and OTP
     */
    body?: UserOtpLoginRequestBody;
    path?: never;
    query?: never;
    url: '/api/users/otp/login';
};

export type PostApiUsersOtpLoginErrors = {
    /**
     * Invalid OTP or expired
     */
    401: unknown;
    /**
     * User not found
     */
    404: unknown;
};

export type PostApiUsersOtpLoginResponses = {
    /**
     * User otpLogin response
     */
    201: UserOtpLoginResponse;
};

export type PostApiUsersOtpLoginResponse = PostApiUsersOtpLoginResponses[keyof PostApiUsersOtpLoginResponses];

export type PostApiUsersRefreshTokenData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/users/refresh-token';
};

export type PostApiUsersRefreshTokenErrors = {
    /**
     * Invalid or expired refresh token
     */
    401: unknown;
};

export type PostApiUsersRefreshTokenResponses = {
    /**
     * User refreshToken response
     */
    201: UserRefreshTokenResponse;
};

export type PostApiUsersRefreshTokenResponse = PostApiUsersRefreshTokenResponses[keyof PostApiUsersRefreshTokenResponses];

export type ClientOptions = {
    baseUrl: `${string}://${string}` | (string & {});
};