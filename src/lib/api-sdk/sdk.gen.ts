// This file is auto-generated by @hey-api/openapi-ts

import type { Options as ClientOptions, TDataShape, Client } from './client';
import type { GetApiMediaData, GetApiMediaResponses, PostApiMediaData, PostApiMediaResponses, DeleteApiMediaByIdData, DeleteApiMediaByIdResponses, DeleteApiMediaByIdErrors, GetApiMediaByIdData, GetApiMediaByIdResponses, GetApiMediaByIdErrors, PatchApiMediaByIdData, PatchApiMediaByIdResponses, PatchApiMediaByIdErrors, GetApiUsersData, GetApiUsersResponses, PostApiUsersData, PostApiUsersResponses, DeleteApiUsersByIdData, DeleteApiUsersByIdResponses, DeleteApiUsersByIdErrors, GetApiUsersByIdData, GetApiUsersByIdResponses, GetApiUsersByIdErrors, PatchApiUsersByIdData, PatchApiUsersByIdResponses, PatchApiUsersByIdErrors, GetApiTeamsData, GetApiTeamsResponses, PostApiTeamsData, PostApiTeamsResponses, DeleteApiTeamsByIdData, DeleteApiTeamsByIdResponses, DeleteApiTeamsByIdErrors, GetApiTeamsByIdData, GetApiTeamsByIdResponses, GetApiTeamsByIdErrors, PatchApiTeamsByIdData, PatchApiTeamsByIdResponses, PatchApiTeamsByIdErrors, PostApiUsersOtpRequestData, PostApiUsersOtpRequestResponses, PostApiUsersOtpRequestErrors, PostApiUsersOtpLoginData, PostApiUsersOtpLoginResponses, PostApiUsersOtpLoginErrors, PostApiUsersRefreshTokenData, PostApiUsersRefreshTokenResponses, PostApiUsersRefreshTokenErrors } from './types.gen';
import { client as _heyApiClient } from './client.gen';

export type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<TData, ThrowOnError> & {
    /**
     * You can provide a client instance returned by `createClient()` instead of
     * individual options. This might be also useful if you want to implement a
     * custom client.
     */
    client?: Client;
    /**
     * You can pass arbitrary values through the `meta` object. This can be
     * used to access values that aren't defined as part of the SDK function.
     */
    meta?: Record<string, unknown>;
};

/**
 * Retrieve a list of Media
 */
export const getApiMedia = <ThrowOnError extends boolean = false>(options?: Options<GetApiMediaData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiMediaResponses, unknown, ThrowOnError>({
        url: '/api/media',
        ...options
    });
};

/**
 * Create a new Media
 */
export const postApiMedia = <ThrowOnError extends boolean = false>(options?: Options<PostApiMediaData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiMediaResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/media',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a Media
 */
export const deleteApiMediaById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiMediaByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiMediaByIdResponses, DeleteApiMediaByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/media/{id}',
        ...options
    });
};

/**
 * Find a Media by ID
 */
export const getApiMediaById = <ThrowOnError extends boolean = false>(options: Options<GetApiMediaByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiMediaByIdResponses, GetApiMediaByIdErrors, ThrowOnError>({
        url: '/api/media/{id}',
        ...options
    });
};

/**
 * Update a Media
 */
export const patchApiMediaById = <ThrowOnError extends boolean = false>(options: Options<PatchApiMediaByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiMediaByIdResponses, PatchApiMediaByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/media/{id}',
        ...options
    });
};

/**
 * Retrieve a list of Users
 */
export const getApiUsers = <ThrowOnError extends boolean = false>(options?: Options<GetApiUsersData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiUsersResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/users',
        ...options
    });
};

/**
 * Create a new User
 */
export const postApiUsers = <ThrowOnError extends boolean = false>(options?: Options<PostApiUsersData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiUsersResponses, unknown, ThrowOnError>({
        url: '/api/users',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a User
 */
export const deleteApiUsersById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiUsersByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiUsersByIdResponses, DeleteApiUsersByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/users/{id}',
        ...options
    });
};

/**
 * Find a User by ID
 */
export const getApiUsersById = <ThrowOnError extends boolean = false>(options: Options<GetApiUsersByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiUsersByIdResponses, GetApiUsersByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/users/{id}',
        ...options
    });
};

/**
 * Update a User
 */
export const patchApiUsersById = <ThrowOnError extends boolean = false>(options: Options<PatchApiUsersByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiUsersByIdResponses, PatchApiUsersByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/users/{id}',
        ...options
    });
};

/**
 * Retrieve a list of Teams
 */
export const getApiTeams = <ThrowOnError extends boolean = false>(options?: Options<GetApiTeamsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiTeamsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/teams',
        ...options
    });
};

/**
 * Create a new Team
 */
export const postApiTeams = <ThrowOnError extends boolean = false>(options?: Options<PostApiTeamsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiTeamsResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/teams',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a Team
 */
export const deleteApiTeamsById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiTeamsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiTeamsByIdResponses, DeleteApiTeamsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/teams/{id}',
        ...options
    });
};

/**
 * Find a Team by ID
 */
export const getApiTeamsById = <ThrowOnError extends boolean = false>(options: Options<GetApiTeamsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiTeamsByIdResponses, GetApiTeamsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/teams/{id}',
        ...options
    });
};

/**
 * Update a Team
 */
export const patchApiTeamsById = <ThrowOnError extends boolean = false>(options: Options<PatchApiTeamsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiTeamsByIdResponses, PatchApiTeamsByIdErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/teams/{id}',
        ...options
    });
};

/**
 * Request OTP for login
 * Send OTP to user email for authentication
 */
export const postApiUsersOtpRequest = <ThrowOnError extends boolean = false>(options?: Options<PostApiUsersOtpRequestData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiUsersOtpRequestResponses, PostApiUsersOtpRequestErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/users/otp/request',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Login with OTP
 * Authenticate user using email and OTP
 */
export const postApiUsersOtpLogin = <ThrowOnError extends boolean = false>(options?: Options<PostApiUsersOtpLoginData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiUsersOtpLoginResponses, PostApiUsersOtpLoginErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/users/otp/login',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Refresh authentication token
 * Get new access token using refresh token
 */
export const postApiUsersRefreshToken = <ThrowOnError extends boolean = false>(options?: Options<PostApiUsersRefreshTokenData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiUsersRefreshTokenResponses, PostApiUsersRefreshTokenErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/users/refresh-token',
        ...options
    });
};