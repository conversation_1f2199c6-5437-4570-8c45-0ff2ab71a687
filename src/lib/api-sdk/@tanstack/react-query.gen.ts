// This file is auto-generated by @hey-api/openapi-ts

import { type Options, getApiMedia, postApiMedia, deleteApiMediaById, getApiMediaById, patchApiMediaById, getApiUsers, postApiUsers, deleteApiUsersById, getApiUsersById, patchApiUsersById, getApiTeams, postApiTeams, deleteApiTeamsById, getApiTeamsById, patchApiTeamsById, postApiUsersOtpRequest, postApiUsersOtpLogin, postApiUsersRefreshToken } from '../sdk.gen';
import { queryOptions, infiniteQueryOptions, type InfiniteData, type DefaultError, type UseMutationOptions } from '@tanstack/react-query';
import type { GetApiMediaData, GetApiMediaResponse, PostApiMediaData, PostApiMediaResponse, DeleteApiMediaByIdData, DeleteApiMediaByIdResponse, GetApiMediaByIdData, PatchApiMediaByIdData, PatchApiMediaByIdResponse, GetApiUsersData, GetApiUsersResponse, PostApiUsersData, PostApiUsersResponse, DeleteApiUsersByIdData, DeleteApiUsersByIdResponse, GetApiUsersByIdData, PatchApiUsersByIdData, PatchApiUsersByIdResponse, GetApiTeamsData, GetApiTeamsResponse, PostApiTeamsData, PostApiTeamsResponse, DeleteApiTeamsByIdData, DeleteApiTeamsByIdResponse, GetApiTeamsByIdData, PatchApiTeamsByIdData, PatchApiTeamsByIdResponse, PostApiUsersOtpRequestData, PostApiUsersOtpRequestResponse, PostApiUsersOtpLoginData, PostApiUsersOtpLoginResponse, PostApiUsersRefreshTokenData, PostApiUsersRefreshTokenResponse } from '../types.gen';
import { client as _heyApiClient } from '../client.gen';

export type QueryKey<TOptions extends Options> = [
    Pick<TOptions, 'baseUrl' | 'body' | 'headers' | 'path' | 'query'> & {
        _id: string;
        _infinite?: boolean;
    }
];

const createQueryKey = <TOptions extends Options>(id: string, options?: TOptions, infinite?: boolean): [
    QueryKey<TOptions>[0]
] => {
    const params: QueryKey<TOptions>[0] = { _id: id, baseUrl: (options?.client ?? _heyApiClient).getConfig().baseUrl } as QueryKey<TOptions>[0];
    if (infinite) {
        params._infinite = infinite;
    }
    if (options?.body) {
        params.body = options.body;
    }
    if (options?.headers) {
        params.headers = options.headers;
    }
    if (options?.path) {
        params.path = options.path;
    }
    if (options?.query) {
        params.query = options.query;
    }
    return [
        params
    ];
};

export const getApiMediaQueryKey = (options?: Options<GetApiMediaData>) => createQueryKey('getApiMedia', options);

/**
 * Retrieve a list of Media
 */
export const getApiMediaOptions = (options?: Options<GetApiMediaData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiMedia({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiMediaQueryKey(options)
    });
};

const createInfiniteParams = <K extends Pick<QueryKey<Options>[0], 'body' | 'headers' | 'path' | 'query'>>(queryKey: QueryKey<Options>, page: K) => {
    const params = {
        ...queryKey[0]
    };
    if (page.body) {
        params.body = {
            ...queryKey[0].body as any,
            ...page.body as any
        };
    }
    if (page.headers) {
        params.headers = {
            ...queryKey[0].headers,
            ...page.headers
        };
    }
    if (page.path) {
        params.path = {
            ...queryKey[0].path as any,
            ...page.path as any
        };
    }
    if (page.query) {
        params.query = {
            ...queryKey[0].query as any,
            ...page.query as any
        };
    }
    return params as unknown as typeof page;
};

export const getApiMediaInfiniteQueryKey = (options?: Options<GetApiMediaData>): QueryKey<Options<GetApiMediaData>> => createQueryKey('getApiMedia', options, true);

/**
 * Retrieve a list of Media
 */
export const getApiMediaInfiniteOptions = (options?: Options<GetApiMediaData>) => {
    return infiniteQueryOptions<GetApiMediaResponse, DefaultError, InfiniteData<GetApiMediaResponse>, QueryKey<Options<GetApiMediaData>>, number | Pick<QueryKey<Options<GetApiMediaData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiMediaData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    page: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiMedia({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiMediaInfiniteQueryKey(options)
    });
};

export const postApiMediaQueryKey = (options?: Options<PostApiMediaData>) => createQueryKey('postApiMedia', options);

/**
 * Create a new Media
 */
export const postApiMediaOptions = (options?: Options<PostApiMediaData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiMedia({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiMediaQueryKey(options)
    });
};

/**
 * Create a new Media
 */
export const postApiMediaMutation = (options?: Partial<Options<PostApiMediaData>>): UseMutationOptions<PostApiMediaResponse, DefaultError, Options<PostApiMediaData>> => {
    const mutationOptions: UseMutationOptions<PostApiMediaResponse, DefaultError, Options<PostApiMediaData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiMedia({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete a Media
 */
export const deleteApiMediaByIdMutation = (options?: Partial<Options<DeleteApiMediaByIdData>>): UseMutationOptions<DeleteApiMediaByIdResponse, DefaultError, Options<DeleteApiMediaByIdData>> => {
    const mutationOptions: UseMutationOptions<DeleteApiMediaByIdResponse, DefaultError, Options<DeleteApiMediaByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteApiMediaById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiMediaByIdQueryKey = (options: Options<GetApiMediaByIdData>) => createQueryKey('getApiMediaById', options);

/**
 * Find a Media by ID
 */
export const getApiMediaByIdOptions = (options: Options<GetApiMediaByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiMediaById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiMediaByIdQueryKey(options)
    });
};

/**
 * Update a Media
 */
export const patchApiMediaByIdMutation = (options?: Partial<Options<PatchApiMediaByIdData>>): UseMutationOptions<PatchApiMediaByIdResponse, DefaultError, Options<PatchApiMediaByIdData>> => {
    const mutationOptions: UseMutationOptions<PatchApiMediaByIdResponse, DefaultError, Options<PatchApiMediaByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await patchApiMediaById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiUsersQueryKey = (options?: Options<GetApiUsersData>) => createQueryKey('getApiUsers', options);

/**
 * Retrieve a list of Users
 */
export const getApiUsersOptions = (options?: Options<GetApiUsersData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiUsers({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiUsersQueryKey(options)
    });
};

export const getApiUsersInfiniteQueryKey = (options?: Options<GetApiUsersData>): QueryKey<Options<GetApiUsersData>> => createQueryKey('getApiUsers', options, true);

/**
 * Retrieve a list of Users
 */
export const getApiUsersInfiniteOptions = (options?: Options<GetApiUsersData>) => {
    return infiniteQueryOptions<GetApiUsersResponse, DefaultError, InfiniteData<GetApiUsersResponse>, QueryKey<Options<GetApiUsersData>>, number | Pick<QueryKey<Options<GetApiUsersData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiUsersData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    page: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiUsers({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiUsersInfiniteQueryKey(options)
    });
};

export const postApiUsersQueryKey = (options?: Options<PostApiUsersData>) => createQueryKey('postApiUsers', options);

/**
 * Create a new User
 */
export const postApiUsersOptions = (options?: Options<PostApiUsersData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiUsers({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiUsersQueryKey(options)
    });
};

/**
 * Create a new User
 */
export const postApiUsersMutation = (options?: Partial<Options<PostApiUsersData>>): UseMutationOptions<PostApiUsersResponse, DefaultError, Options<PostApiUsersData>> => {
    const mutationOptions: UseMutationOptions<PostApiUsersResponse, DefaultError, Options<PostApiUsersData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiUsers({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete a User
 */
export const deleteApiUsersByIdMutation = (options?: Partial<Options<DeleteApiUsersByIdData>>): UseMutationOptions<DeleteApiUsersByIdResponse, DefaultError, Options<DeleteApiUsersByIdData>> => {
    const mutationOptions: UseMutationOptions<DeleteApiUsersByIdResponse, DefaultError, Options<DeleteApiUsersByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteApiUsersById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiUsersByIdQueryKey = (options: Options<GetApiUsersByIdData>) => createQueryKey('getApiUsersById', options);

/**
 * Find a User by ID
 */
export const getApiUsersByIdOptions = (options: Options<GetApiUsersByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiUsersById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiUsersByIdQueryKey(options)
    });
};

/**
 * Update a User
 */
export const patchApiUsersByIdMutation = (options?: Partial<Options<PatchApiUsersByIdData>>): UseMutationOptions<PatchApiUsersByIdResponse, DefaultError, Options<PatchApiUsersByIdData>> => {
    const mutationOptions: UseMutationOptions<PatchApiUsersByIdResponse, DefaultError, Options<PatchApiUsersByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await patchApiUsersById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiTeamsQueryKey = (options?: Options<GetApiTeamsData>) => createQueryKey('getApiTeams', options);

/**
 * Retrieve a list of Teams
 */
export const getApiTeamsOptions = (options?: Options<GetApiTeamsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiTeams({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiTeamsQueryKey(options)
    });
};

export const getApiTeamsInfiniteQueryKey = (options?: Options<GetApiTeamsData>): QueryKey<Options<GetApiTeamsData>> => createQueryKey('getApiTeams', options, true);

/**
 * Retrieve a list of Teams
 */
export const getApiTeamsInfiniteOptions = (options?: Options<GetApiTeamsData>) => {
    return infiniteQueryOptions<GetApiTeamsResponse, DefaultError, InfiniteData<GetApiTeamsResponse>, QueryKey<Options<GetApiTeamsData>>, number | Pick<QueryKey<Options<GetApiTeamsData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiTeamsData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    page: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiTeams({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiTeamsInfiniteQueryKey(options)
    });
};

export const postApiTeamsQueryKey = (options?: Options<PostApiTeamsData>) => createQueryKey('postApiTeams', options);

/**
 * Create a new Team
 */
export const postApiTeamsOptions = (options?: Options<PostApiTeamsData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiTeams({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiTeamsQueryKey(options)
    });
};

/**
 * Create a new Team
 */
export const postApiTeamsMutation = (options?: Partial<Options<PostApiTeamsData>>): UseMutationOptions<PostApiTeamsResponse, DefaultError, Options<PostApiTeamsData>> => {
    const mutationOptions: UseMutationOptions<PostApiTeamsResponse, DefaultError, Options<PostApiTeamsData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiTeams({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

/**
 * Delete a Team
 */
export const deleteApiTeamsByIdMutation = (options?: Partial<Options<DeleteApiTeamsByIdData>>): UseMutationOptions<DeleteApiTeamsByIdResponse, DefaultError, Options<DeleteApiTeamsByIdData>> => {
    const mutationOptions: UseMutationOptions<DeleteApiTeamsByIdResponse, DefaultError, Options<DeleteApiTeamsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteApiTeamsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiTeamsByIdQueryKey = (options: Options<GetApiTeamsByIdData>) => createQueryKey('getApiTeamsById', options);

/**
 * Find a Team by ID
 */
export const getApiTeamsByIdOptions = (options: Options<GetApiTeamsByIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiTeamsById({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiTeamsByIdQueryKey(options)
    });
};

/**
 * Update a Team
 */
export const patchApiTeamsByIdMutation = (options?: Partial<Options<PatchApiTeamsByIdData>>): UseMutationOptions<PatchApiTeamsByIdResponse, DefaultError, Options<PatchApiTeamsByIdData>> => {
    const mutationOptions: UseMutationOptions<PatchApiTeamsByIdResponse, DefaultError, Options<PatchApiTeamsByIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await patchApiTeamsById({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const postApiUsersOtpRequestQueryKey = (options?: Options<PostApiUsersOtpRequestData>) => createQueryKey('postApiUsersOtpRequest', options);

/**
 * Request OTP for login
 * Send OTP to user email for authentication
 */
export const postApiUsersOtpRequestOptions = (options?: Options<PostApiUsersOtpRequestData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiUsersOtpRequest({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiUsersOtpRequestQueryKey(options)
    });
};

/**
 * Request OTP for login
 * Send OTP to user email for authentication
 */
export const postApiUsersOtpRequestMutation = (options?: Partial<Options<PostApiUsersOtpRequestData>>): UseMutationOptions<PostApiUsersOtpRequestResponse, DefaultError, Options<PostApiUsersOtpRequestData>> => {
    const mutationOptions: UseMutationOptions<PostApiUsersOtpRequestResponse, DefaultError, Options<PostApiUsersOtpRequestData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiUsersOtpRequest({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const postApiUsersOtpLoginQueryKey = (options?: Options<PostApiUsersOtpLoginData>) => createQueryKey('postApiUsersOtpLogin', options);

/**
 * Login with OTP
 * Authenticate user using email and OTP
 */
export const postApiUsersOtpLoginOptions = (options?: Options<PostApiUsersOtpLoginData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiUsersOtpLogin({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiUsersOtpLoginQueryKey(options)
    });
};

/**
 * Login with OTP
 * Authenticate user using email and OTP
 */
export const postApiUsersOtpLoginMutation = (options?: Partial<Options<PostApiUsersOtpLoginData>>): UseMutationOptions<PostApiUsersOtpLoginResponse, DefaultError, Options<PostApiUsersOtpLoginData>> => {
    const mutationOptions: UseMutationOptions<PostApiUsersOtpLoginResponse, DefaultError, Options<PostApiUsersOtpLoginData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiUsersOtpLogin({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const postApiUsersRefreshTokenQueryKey = (options?: Options<PostApiUsersRefreshTokenData>) => createQueryKey('postApiUsersRefreshToken', options);

/**
 * Refresh authentication token
 * Get new access token using refresh token
 */
export const postApiUsersRefreshTokenOptions = (options?: Options<PostApiUsersRefreshTokenData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiUsersRefreshToken({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiUsersRefreshTokenQueryKey(options)
    });
};

/**
 * Refresh authentication token
 * Get new access token using refresh token
 */
export const postApiUsersRefreshTokenMutation = (options?: Partial<Options<PostApiUsersRefreshTokenData>>): UseMutationOptions<PostApiUsersRefreshTokenResponse, DefaultError, Options<PostApiUsersRefreshTokenData>> => {
    const mutationOptions: UseMutationOptions<PostApiUsersRefreshTokenResponse, DefaultError, Options<PostApiUsersRefreshTokenData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiUsersRefreshToken({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};