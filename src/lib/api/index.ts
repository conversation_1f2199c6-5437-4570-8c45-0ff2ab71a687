import { payload } from '@/constants'
import { client } from '@/lib/api-sdk/client.gen'
import { getServerSideURL } from '@/utils/server/getServerSideUrl'

client.setConfig({
  baseUrl: getServerSideURL()
})

// Server-side authentication setup
if (typeof window === 'undefined') {
  import('next/headers').then((module) => {
    const cookies = module.cookies

    client.interceptors.request.use(async (options) => {
      const cookieStore = await cookies()
      const token = cookieStore.get(payload.authTokenId)?.value

      if (token) {
        if (!options.headers) {
          options.headers = new Headers()
        }
        if (options.headers instanceof Headers) {
          options.headers.set('Authorization', `Bearer ${token}`)
        } else if (typeof options.headers === 'object') {
          ;(options.headers as Record<string, string>)['Authorization'] =
            `Bearer ${token}`
        }
      }
    })
  })
}

export * as api from '@/lib/api-sdk/sdk.gen'
export * from '@/lib/api-sdk/@tanstack/react-query.gen'
export * from '@/lib/api-sdk/types.gen'
export { client }
