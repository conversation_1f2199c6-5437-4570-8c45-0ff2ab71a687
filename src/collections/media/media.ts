import type { CollectionConfig } from 'payload'
import { anyone, createUpdateDelete } from '@/collections/access'
import { AdminCollectionsGroup, collections } from '@/constants'
import {
  FixedToolbarFeature,
  InlineToolbarFeature,
  lexicalEditor
} from '@payloadcms/richtext-lexical'

const {
  media: { slug: mediaSlug }
} = collections

export const media: CollectionConfig<typeof mediaSlug> = {
  slug: mediaSlug,
  access: {
    create: createUpdateDelete,
    delete: createUpdateDelete,
    read: anyone,
    update: createUpdateDelete
  },
  admin: {
    group: AdminCollectionsGroup.marketing
  },
  fields: [
    {
      name: 'alt',
      type: 'text'
      //required: true,
    },
    {
      name: 'caption',
      type: 'richText',
      editor: lexicalEditor({
        features: ({ rootFeatures }) => {
          return [...rootFeatures, FixedToolbarFeature(), InlineToolbarFeature()]
        }
      })
    }
  ],
  upload: {
    adminThumbnail: 'thumbnail',
    focalPoint: true,
    imageSizes: [
      {
        name: 'thumbnail',
        width: 300
      },
      {
        name: 'square',
        width: 500,
        height: 500
      },
      {
        name: 'small',
        width: 600
      },
      {
        name: 'medium',
        width: 900
      },
      {
        name: 'large',
        width: 1400
      },
      {
        name: 'xlarge',
        width: 1920
      },
      {
        name: 'og',
        width: 1200,
        height: 630,
        crop: 'center'
      }
    ]
  }
}
