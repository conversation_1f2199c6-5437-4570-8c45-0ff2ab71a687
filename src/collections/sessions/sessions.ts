import type { CollectionConfig } from 'payload'
import { isSuperAdminAccess } from '@/collections/access'
import { AdminCollectionsGroup } from '@/constants'

export const sessions: CollectionConfig = {
  slug: 'sessions',
  access: {
    create: isSuperAdminAccess,
    read: isSuperAdminAccess,
    update: isSuperAdminAccess,
    delete: isSuperAdminAccess
  },
  admin: {
    useAsTitle: 'id',
    group: AdminCollectionsGroup.application
  },
  fields: [
    {
      name: 'id',
      type: 'text',
      required: true,
      unique: true,
      admin: {
        readOnly: true
      }
    },
    {
      name: 'familyId',
      type: 'text',
      required: true,
      unique: true
    },
    {
      name: 'userId',
      type: 'text',
      required: true
    },
    {
      name: 'token',
      type: 'text',
      required: true
    },
    {
      name: 'expiresAt',
      type: 'date',
      required: true
    },
    {
      name: 'ipAddress',
      type: 'text'
    },
    {
      name: 'userAgent',
      type: 'text'
    }
  ],
  timestamps: true
}
