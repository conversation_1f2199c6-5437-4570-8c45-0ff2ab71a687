import type { CollectionConfig } from 'payload'
import { isSuperAdminAccess } from '@/collections/access'
import { AdminCollectionsGroup, collections } from '@/constants'

const {
  products: { slug: productsSlug }
} = collections

export const products: CollectionConfig = {
  slug: productsSlug,
  access: {
    create: isSuperAdminAccess,
    read: isSuperAdminAccess,
    update: isSuperAdminAccess,
    delete: isSuperAdminAccess
  },
  admin: {
    useAsTitle: 'name',
    group: AdminCollectionsGroup.application
  },
  fields: [
    {
      name: 'name',
      type: 'text'
    },
    {
      name: 'description',
      type: 'text'
    }
  ],
  timestamps: true
}
