import type { CollectionConfig } from 'payload'
import { isUserOrSuperAdmin } from '@/collections/access'
import { AdminCollectionsGroup, collections } from '@/constants'
import { isTeamOwnerOrSuperAdmin, readTeamAccess } from './access'

const {
  keywords: { slug: keywordsSlug }
} = collections

export const keywords: CollectionConfig = {
  slug: keywordsSlug,
  access: {
    create: isUserOrSuperAdmin,
    read: readTeamAccess,
    update: isTeamOwnerOrSuperAdmin,
    delete: isTeamOwnerOrSuperAdmin
  },
  admin: {
    useAsTitle: 'keyword',
    group: AdminCollectionsGroup.application
  },
  fields: [
    {
      name: 'keyword',
      type: 'text',
      required: true
    },
    {
      name: 'location',
      type: 'text',
      required: true
    },
    {
      name: 'refreshInterval',
      type: 'radio',
      required: true,
      options: [
        {
          label: 'Hourly',
          value: 'hourly'
        },
        {
          label: 'Daily',
          value: 'daily'
        },
        {
          label: 'Weekly',
          value: 'weekly'
        },
        {
          label: 'Monthly',
          value: 'monthly'
        }
      ]
    },
    {
      name: 'device',
      type: 'select',
      hasMany: true,
      required: true,
      options: [
        {
          label: 'Desktop',
          value: 'desktop'
        },
        {
          label: 'Mobile',
          value: 'mobile'
        }
      ]
    },
    {
      name: 'tags',
      type: 'array',
      fields: [
        {
          name: 'tag',
          type: 'text'
        }
      ]
    },
    {
      name: 'analytics',
      type: 'array',
      fields: [
        {
          name: 'rankDistrubution',
          type: 'number'
        },
        {
          name: 'serpFeatures',
          type: 'number'
        },
        {
          name: 'volatility',
          type: 'number'
        }
      ]
    },
    {
      name: 'project',
      type: 'relationship',
      relationTo: 'projects',
      hasMany: false,
      required: true,
      admin: {
        position: 'sidebar'
      }
    },
    {
      name: 'user',
      type: 'relationship',
      relationTo: 'users',
      hasMany: false,
      required: true,
      admin: {
        position: 'sidebar'
      }
    }
  ],
  timestamps: true
}
