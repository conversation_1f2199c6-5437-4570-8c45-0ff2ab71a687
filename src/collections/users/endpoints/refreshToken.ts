import { jwtVerify } from 'jose'
import { ValidationError, addDataAndFileToRequest, jwtSign } from 'payload'
import type { Endpoint, PayloadRequest } from 'payload'
import { collections } from '@/constants'
import { generateSerpCookie } from '@/utils/server'

type JWTToken = {
  collection: string
  id: string
}

const {
  users: { slug, accessTokenExpiration, refreshTokenExpiration },
  sessions: { slug: sessionsSlug }
} = collections

export const refreshToken: Endpoint = {
  method: 'post',
  path: '/refresh-token',
  handler: async (req: PayloadRequest) => {
    const { payload } = req
    const collectionConfig = payload.collections[slug].config

    await addDataAndFileToRequest(req)

    const refreshToken = req.data?.token

    if (!refreshToken) {
      throw new ValidationError({
        collection: slug,
        errors: [
          {
            message: 'Refresh token is required.',
            path: 'refreshToken'
          }
        ]
      })
    }

    const secretKey = new TextEncoder().encode(payload.secret)
    const { payload: decodedPayload } = await jwtVerify<JWTToken>(refreshToken, secretKey)

    if (!decodedPayload) {
      throw new ValidationError({
        collection: slug,
        errors: [
          {
            message: 'Invalid refresh token.',
            path: 'refreshToken'
          }
        ]
      })
    }

    const session = await payload.db.findOne({
      collection: sessionsSlug,
      where: {
        token: {
          equals: refreshToken
        },
        userId: {
          equals: decodedPayload.userId
        }
      }
    })

    const familyId = decodedPayload.familyId

    if (!session) {
      const sessions = await payload.db.find({
        collection: sessionsSlug,
        where: {
          familyId: {
            equals: familyId
          },
          userId: {
            equals: decodedPayload.userId
          }
        }
      })

      if (sessions.totalDocs > 0) {
        await payload.db.deleteMany({
          collection: sessionsSlug,
          where: {
            familyId: {
              equals: familyId
            },
            userId: {
              equals: decodedPayload.userId
            }
          }
        })
      }

      throw new ValidationError({
        collection: slug,
        errors: [
          {
            message: 'Invalid refresh token.',
            path: 'refreshToken'
          }
        ]
      })
    }

    const { token: accessToken, exp: accessTokenExp } = await jwtSign({
      fieldsToSign: { userId: decodedPayload.userId },
      secret: payload.secret,
      tokenExpiration: accessTokenExpiration
    })

    const { token: newRefreshToken, exp: refreshTokenExp } = await jwtSign({
      fieldsToSign: { userId: decodedPayload.userId, familyId: decodedPayload.familyId },
      secret: payload.secret,
      tokenExpiration: refreshTokenExpiration
    })

    await payload.update({
      collection: sessionsSlug,
      id: session.id,
      data: {
        token: newRefreshToken,
        expiresAt: new Date(refreshTokenExp * 1000).toISOString()
      }
    })

    const cookie = generateSerpCookie({
      collectionAuthConfig: collectionConfig.auth,
      cookiePrefix: `${session.id}:${decodedPayload.userId}:` as string,
      token: accessToken
    })

    const result: Record<string, unknown> = {
      exp: accessTokenExp,
      message: 'Successfully refreshed token.',
      token: newRefreshToken
    }

    return Response.json(result, {
      headers: new Headers({
        'Set-Cookie': cookie as string
      })
    })
  }
}
