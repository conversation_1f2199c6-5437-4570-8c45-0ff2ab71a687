import type { Endpoint, PayloadRequest, Where } from 'payload'
import { APIError, ValidationError, addDataAndFileToRequest } from 'payload'
import type {
  GenerateOTPEmailHTML,
  GenerateOTPEmailSubject
} from '@/collections/users/types'
import { collections } from '@/constants'
import { encrypt, generateOTP } from '@/utils/server'

const {
  users: { slug, otpExpiration: exp, endpoints }
} = collections

export const requestOtp: Endpoint = {
  method: 'post',
  path: endpoints.requestOtp,
  handler: async (req: PayloadRequest) => {
    await addDataAndFileToRequest(req)

    const { payload } = req

    const data = req.data

    let user

    const unsanitizedEmail = data?.email

    const sanitizedEmail =
      typeof unsanitizedEmail === 'string' ? unsanitizedEmail.toLowerCase().trim() : null

    if (!sanitizedEmail) {
      throw new ValidationError({
        collection: slug,
        errors: [{ message: req.i18n.t('validation:required'), path: 'email' }]
      })
    }

    const whereConstraint: Where = {
      email: {
        equals: sanitizedEmail
      }
    }

    user = await payload.db.findOne({
      collection: slug,
      req,
      where: whereConstraint
    })

    if (!user) {
      user = await payload.create({
        collection: slug,
        data: {
          email: sanitizedEmail
        },
        req,
        select: {
          id: true,
          email: true
        }
      })
    }

    const userId = user.id

    const otp = generateOTP()
    const _otpExpiration = new Date(Date.now() + exp * 1000).toISOString()

    try {
      await payload.update({
        collection: slug,
        id: userId,
        data: {
          _otp: encrypt({ payload, value: otp }),
          _otpExpiration: _otpExpiration
        },
        depth: 0,
        req
      })
    } catch (error) {
      const errorMessage = `Error setting one-time password for Email: ${sanitizedEmail}`
      payload.logger.error(error, errorMessage)
      throw new APIError(errorMessage)
    }

    const subject = getDefaultOTPEmailSubject({ collection: slug, otp, user })
    const html = getDefaultOTPEmailHTML({ collection: slug, otp, user })

    if ('email' in user) {
      await payload.email.sendEmail({
        from: `"${payload.email.defaultFromName}" <${payload.email.defaultFromAddress}>`,
        html,
        subject,
        to: user.email
      })
    } else {
      throw new APIError(
        `Attempted to send email to user with Email: ${sanitizedEmail}, but user has no email specified.`
      )
    }

    return Response.json({
      type: 'Email',
      message: 'Successfully sent one-time password.',
      value: sanitizedEmail
    })
  }
}

export const getDefaultOTPEmailHTML: GenerateOTPEmailHTML = ({ otp }) =>
  `You are receiving this because you (or someone else) have requested a one-time password to log into your account. The code is <strong>${otp}</strong>.`

export const getDefaultOTPEmailSubject: GenerateOTPEmailSubject = () =>
  `One-time password login`
