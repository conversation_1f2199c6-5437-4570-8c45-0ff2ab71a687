import { nanoid } from 'nanoid'
import type { Endpoint, PayloadRequest, TypedUser } from 'payload'
import { ValidationError, addDataAndFileToRequest, jwtSign } from 'payload'
import { findUser } from '@/collections/users/utils'
import { collections } from '@/constants'
import { encrypt, generateSerpCookie, getIp, getUserAgent } from '@/utils/server'

const {
  users: { slug, accessTokenExpiration, refreshTokenExpiration, endpoints }
} = collections

export const login: Endpoint = {
  method: 'post',
  path: endpoints.otpLogin,
  handler: async (req: PayloadRequest) => {
    const collectionConfig = req.payload.collections[slug].config
    const { payload } = req

    await addDataAndFileToRequest(req)

    const email = req.data?.email
    const otp = req.data?.otp
    const type = 'email'

    if (!email) {
      throw new ValidationError({
        collection: collectionConfig.slug,
        errors: [
          {
            message: 'Please provide an email.',
            path: 'otp'
          }
        ]
      })
    }

    if (!otp) {
      throw new ValidationError({
        collection: collectionConfig.slug,
        errors: [
          {
            message: 'One-time password is required.',
            path: 'otp'
          }
        ]
      })
    }

    const matchedUser = await findUser({
      type,
      collection: slug,
      otp: encrypt({ payload, value: otp }),
      payload,
      value: email
    })

    if (!matchedUser) {
      throw new ValidationError({
        collection: collectionConfig.slug,
        errors: [
          {
            message: 'Failed logging in with one-time password.',
            path: 'otp'
          }
        ]
      })
    }

    const user: TypedUser = {
      ...matchedUser,
      collection: slug
    }

    req.user = user

    const familyId = nanoid()
    const sessionId = nanoid()

    // create access token
    const { token: accessToken, exp: accessTokenExp } = await jwtSign({
      fieldsToSign: { userId: user.id },
      secret: payload.secret,
      tokenExpiration: accessTokenExpiration
    })

    // create refresh token
    const { token: refreshToken, exp: refreshTokenExp } = await jwtSign({
      fieldsToSign: { userId: user.id, familyId: familyId },
      secret: payload.secret,
      tokenExpiration: refreshTokenExpiration
    })

    // create session

    await payload.db.create({
      collection: 'sessions',
      data: {
        id: sessionId,
        familyId: familyId,
        userId: user.id,
        token: refreshToken,
        expiresAt: new Date(refreshTokenExp * 1000).toISOString(),
        ipAddress: getIp(req),
        userAgent: getUserAgent(req)
      }
    })

    const dataToUpdate: Record<string, unknown> = {
      _otp: null,
      _otpExpiration: null
    }

    await payload.update({
      id: user.id,
      collection: slug,
      data: dataToUpdate,
      depth: 0,
      req
    })

    const headers = new Headers()

    const accessTokenCookie = generateSerpCookie({
      collectionAuthConfig: collectionConfig.auth,
      cookiePrefix: user.id,
      token: accessToken
    })

    const currentUserCookie = generateSerpCookie({
      collectionAuthConfig: collectionConfig.auth,
      cookiePrefix: 'currentUser',
      token: user.id
    })

    headers.append('Set-Cookie', accessTokenCookie as string)
    headers.append('Set-Cookie', currentUserCookie as string)

    const result: Record<string, unknown> = {
      exp: accessTokenExp,
      message: 'Successfully logged in with one-time password.',
      user,
      token: refreshToken
    }

    return Response.json(result, {
      headers: headers
    })
  }
}
