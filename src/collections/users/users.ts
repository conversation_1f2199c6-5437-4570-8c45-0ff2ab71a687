import { nanoid } from 'nanoid'
import type { CollectionConfig } from 'payload'
import { anyone, isSelfOrSuperAdmin, isSuperAdminAccess } from '@/collections/access'
import { AdminCollectionsGroup, collections } from '@/constants'
import { tenantsArrayField } from '@payloadcms/plugin-multi-tenant/fields'
import { login, refreshToken, requestOtp } from './endpoints'
import { setMultiAccountsCookie } from './hooks'
import { jwtLocal } from './strategies'

const defaultTenantArrayField = tenantsArrayField({
  tenantsArrayFieldName: 'teams',
  tenantsArrayTenantFieldName: 'team',
  tenantsCollectionSlug: 'teams',
  arrayFieldAccess: {},
  tenantFieldAccess: {},
  rowFields: [
    {
      name: 'roles',
      type: 'select',
      defaultValue: ['teamviewer'],
      hasMany: true,
      options: ['teamadmin', 'teamviewer'],
      required: true
    }
  ]
})

const {
  users: { slug: usersSlug }
} = collections

export const users: CollectionConfig = {
  slug: usersSlug,
  access: {
    admin: isSuperAdminAccess,
    create: anyone,
    delete: isSuperAdminAccess,
    read: isSelfOrSuperAdmin,
    update: isSelfOrSuperAdmin
  },
  admin: {
    defaultColumns: ['firstName', 'email'],
    useAsTitle: 'firstName',
    group: AdminCollectionsGroup.application
  },
  auth: {
    disableLocalStrategy: true,
    strategies: [
      {
        name: 'serplens-jwt',
        authenticate: jwtLocal
      }
    ]
  },
  fields: [
    {
      name: 'id',
      type: 'text',
      required: true,
      unique: true,
      admin: {
        readOnly: true
      },
      defaultValue: () => nanoid()
    },
    {
      name: 'email',
      type: 'email',
      required: true,
      unique: true
    },
    {
      name: 'firstName',
      type: 'text'
    },
    {
      name: 'lastName',
      type: 'text'
    },
    {
      name: 'profileImage',
      type: 'upload',
      relationTo: 'media',
      admin: {
        description: 'Upload a profile image for the user',
        position: 'sidebar'
      }
    },
    {
      type: 'array',
      name: 'socialAccounts',
      admin: {
        description: 'Social accounts for the user',
        position: 'sidebar'
      },
      fields: [
        {
          name: 'provider',
          type: 'text',
          required: true,
          unique: true
        },
        {
          name: 'providerId',
          type: 'text',
          required: true,
          unique: true
        },
        {
          name: 'isActive',
          type: 'checkbox',
          defaultValue: true
        },
        {
          name: 'linkedAt',
          type: 'date',
          defaultValue: new Date()
        }
      ]
    },
    {
      ...defaultTenantArrayField,
      saveToJWT: false,
      admin: {
        ...(defaultTenantArrayField?.admin || {}),
        position: 'sidebar'
      }
    },
    {
      name: '_otp',
      type: 'text',
      hidden: true,
      index: true
    },
    {
      name: '_otpExpiration',
      type: 'date',
      hidden: true,
      index: true
    }
  ],
  timestamps: true,
  hooks: {
    afterLogin: [setMultiAccountsCookie]
  },
  endpoints: [requestOtp, login, refreshToken]
}
