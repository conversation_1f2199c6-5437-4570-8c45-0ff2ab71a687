import type { CollectionConfig } from 'payload'
import { isUserOrSuperAdmin } from '@/collections/access'
import { AdminCollectionsGroup, collections } from '@/constants'
import { isTeamOwnerOrSuperAdmin, readTeamAccess } from './access'

const {
  screenshots: { slug: screenshotsSlug },
  assets: { slug: assetsSlug }
} = collections

export const screenshots: CollectionConfig = {
  slug: screenshotsSlug,
  access: {
    create: isUserOrSuperAdmin,
    read: readTeamAccess,
    update: isTeamOwnerOrSuperAdmin,
    delete: isTeamOwnerOrSuperAdmin
  },
  admin: {
    useAsTitle: 'name',
    group: AdminCollectionsGroup.application
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true
    },
    {
      name: 'user',
      type: 'relationship',
      relationTo: 'users',
      hasMany: false,
      required: true,
      admin: {
        position: 'sidebar'
      }
    },
    {
      name: 'image',
      type: 'relationship',
      relationTo: assetsSlug,
      required: true,
      admin: {
        position: 'sidebar'
      }
    }
  ],
  timestamps: true
}
