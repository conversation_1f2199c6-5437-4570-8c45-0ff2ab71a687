import type { GlobalConfig } from 'payload'
import { anyone } from '@/collections/access'
import { AdminCollectionsGroup, collections } from '@/constants'
import { link } from '@/fields'
import { revalidateHeader } from './hooks'

const {
  header: { slug: headerSlug }
} = collections

export const header: GlobalConfig<typeof headerSlug> = {
  slug: headerSlug,
  access: {
    read: anyone
  },
  admin: {
    group: AdminCollectionsGroup.marketing
  },
  fields: [
    {
      name: 'navItems',
      type: 'array',
      fields: [
        link({
          appearances: false
        })
      ],
      maxRows: 6,
      admin: {
        initCollapsed: true,
        components: {
          RowLabel: '@/header/RowLabel#RowLabel'
        }
      }
    }
  ],
  hooks: {
    afterChange: [revalidateHeader]
  }
}
