import type { CollectionConfig } from 'payload'
import { anyone, createUpdateDelete } from '@/collections/access'
import { AdminCollectionsGroup, collections } from '@/constants'
import { slugField } from '@/fields/slug'

const {
  categories: { slug: categoriesSlug }
} = collections

export const categories: CollectionConfig<typeof categoriesSlug> = {
  slug: categoriesSlug,
  access: {
    create: createUpdateDelete,
    delete: createUpdateDelete,
    read: anyone,
    update: createUpdateDelete
  },
  admin: {
    useAsTitle: 'title',
    group: AdminCollectionsGroup.marketing
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true
    },
    ...slugField()
  ]
}
